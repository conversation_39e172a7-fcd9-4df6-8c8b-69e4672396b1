#/bin/bash
# curl api.dev.insshort.com/deploy to execute this scrpit
source ~/.bashrc
/usr/bin/git clean -xdf -e "log" -e "backend-0.0.1-SNAPSHOT.jar" -e ".gradle"
/usr/bin/git reset -q --hard
/usr/bin/git pull
/usr/bin/git checkout test
/opt/gradle/gradle-7.2/bin/gradle build bootJar -x test --stacktrace
/bin/cp build/libs/backend-0.0.1-SNAPSHOT.jar ./
sudo /usr/bin/systemctl restart insshort-backend.service
