#基础镜像，如果本地没有，会从远程仓库拉取。
# 编译阶段 命名为 builder
FROM gradle:7.2-jdk11 as builder
#镜像的制作人
MAINTAINER shortPlay.com
#工作目录
WORKDIR /home/<USER>/

#项目信息导入到容器中
COPY --chown=gradle:gradle . /home/<USER>/

#gradle -x test ——表明在打包的时候（把通用的类打成jar包供其他服务调用），排除名称为test的Task
RUN gradle build bootJar -x test --stacktrace

# 运行阶段
FROM openjdk:11
# 从编译阶段的中拷贝编译结果到当前镜像中,并更名为app.jar
COPY --from=builder /home/<USER>/build/libs/*.jar /app.jar

#声明了容器应该打开的端口并没有实际上将它打开
EXPOSE 8080

# 标记一个激活的配置环境
ENV SPRING_PROFILES_ACTIVE=test

#指定容器启动时要执行的命令，但如果存在CMD指令，CMD中的参数会被附加到ENTRYPOINT指令的后面
ENTRYPOINT ["java","-jar","app.jar"]
