package com.shortplay.server.service;

import com.shortplay.server.service.impl.oauth.GoogleOauthService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.GeneralSecurityException;

@SpringBootTest
public class GoogleOauthServiceTest {

    @Resource
    private GoogleOauthService googleOauthService;

    @Test
    public void oauth_google_generate_url_test() throws GeneralSecurityException, IOException {
        String url = "https://7579-223-72-43-136.ngrok-free.app/v1/oauth";
        String s = googleOauthService.getAuthorizingUrl(url);
        System.out.println(s);

        String email = googleOauthService.authorizeAndGetEmail("4/0AfJohXmJ4NCwuKHYSP4kWdObWpa8NQX1iO9q_4GvA852W-CVjDKlChILxU8uNL3-NT19ew", "DEBUG");
        System.out.println(email);
    }


}
