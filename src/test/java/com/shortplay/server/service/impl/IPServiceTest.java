package com.shortplay.server.service.impl;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class IPServiceTest {

    @Resource
    private IPService ipService;

    @Test
    void getRequestTZ() {
        ipService.getRequestTZ("*************");
    }
}