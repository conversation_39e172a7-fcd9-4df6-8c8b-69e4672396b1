package com.shortplay.server.service.impl.oauth;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class FacebookOauthServiceTest {

    @Resource
    private FacebookOauthService facebookOauthService;
    @Test
    void authorizeAndGetUserId() {
        String userId = facebookOauthService.authorizeAndGetUserId("EAALbMxm6cz4BOzup4LTS9ZBQDgXQaR15tLZBHjKXRZBw2uYZAQGZAlQ7xyz3fKDfS3sPvThHpI1Ss7EawrWh9ZCq3QYyhclgMZBxNeKCo1Mfnw9903VErxyo5GkbAQ5qDKRN3bKZCYUOLet98ztLZCitBEtZA1VQA4Ug3x9wgEV3sWKUuZC53hCOZBND2FvGhgZDZD\n");
        System.out.println("userId = " + userId);
    }
}