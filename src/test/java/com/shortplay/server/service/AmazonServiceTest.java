package com.shortplay.server.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.github.scribejava.core.pkce.PKCE;
import com.github.scribejava.core.pkce.PKCECodeChallengeMethod;
import com.shortplay.server.component.bo.S3CookieBO;
import com.shortplay.server.service.impl.AmazonService;
import com.twitter.clientlib.auth.TwitterOAuth20Service;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Scanner;

@SpringBootTest
public class AmazonServiceTest {
    @Resource
    private AmazonService amazonService;

    @Test
    public void testSignedGetUrl() {
        String signedUrl = amazonService.getSignedUrl("media_output/359b033b5bb5c9eab44c806ed539b6003bf3b384.jpeg", 7);
//        String signedUrl = amazonService.getSignedUrl("vod-insshort-output/assets/0b3d8e8a-fd38-4523-9eab-b841b49c7bcb/HLS/Kicked_the_bucket_360.m3u8");
        System.out.println(signedUrl);
    }

    @Test
    public void testgetCookie() {
        S3CookieBO s3CookieBO = amazonService.getDefaultCookie("media_output/stride.m3u8", 7);
        System.out.println(s3CookieBO);
    }

    public static void main(String[] args) {
//```curl -b "CloudFront-Expires=1704894580;CloudFront-Signature=C93BmCj7eWglt-IMlcDoMDd5wxLuFtOkftPrfKsCKm5vO5ILGm0VbifOUPl5EBJGQtNBtYjN3uooM8FU0ih9ebpFwzL26w8Jw1i1afj~5Ip35pBkWigsjidVCR3yo~6kmDzj8HMPYdbH~GsPChCLm1k6f06G16Ar45beedgrMLOr4djsRB6QrYFuwdIY2BGKZt3nJi~YoWY~8mNQhnyeL0~3uw4clEFwqiMau7f7S6dpxM2OdwDGBWfcyWDND3UxQHluaOVb3qfkLz~r2OOGCRLERuvwvr0hsPVIEeeIfyWkh-~VjkRMsOxvdppf-vxb1T2ME6Yu1lufwmIPCY~Gsg__;CloudFront-Key-Pair-Id=KHALMN6DY1TW4;" https://d2scetwi9dksiu.cloudfront.net/media_output/stride.m3u8```
//```curl -b "CloudFront-Policy=****************************************************************************************************************************************************************************************************************************************************************************;CloudFront-Key-Pair-Id=KHALMN6DY1TW4;CloudFront-Signature=rne2cuhzjRSNdpLOUTpEoM8b6pEth~XVVu4a2nh2SiJbY5g4MbeOHOXfnvRkSBl6HzpIIgvCgEKCBLwzkaku9L8pDiX6nHquQ80ZEqZUd9a8HIRUWOkptxodKQ99BQ4ePzklACSCqCZDQV7UOeEGz7TUaJKAvHWTX3PGaYS1jY8mFJNHc87Qb8U3RLk4OWl-5nnA7q0rHqOolHH7y3DEI8N67RvelTwJaLGGbk3VnmT0TVOitgTcRs4ONDibOwsbEgdDxNnIPdBGK-idkxA24MWjzEusm8k~OLXaUdXj96d3L04xhHlgG~69FdRDkHRJdzpsLJ646OaBfBBsJSCirQ__;" https://d2scetwi9dksiu.cloudfront.net/media_output/stride.m3u8```

        OAuth2AccessToken accessToken = getAccessToken();
        if (accessToken == null) {
            return;
        }

        // Setting the access & refresh tokens into TwitterCredentialsOAuth2
//        credentials.setTwitterOauth2AccessToken(accessToken.getAccessToken());
//        credentials.setTwitterOauth2RefreshToken(accessToken.getRefreshToken());
    }

    public static OAuth2AccessToken getAccessToken() {
        TwitterOAuth20Service service = new TwitterOAuth20Service(
                "NkFIdHRGNjVrd1FwWHI4TjlBaEY6MTpjaQ",
                "dLw5I-LMhJU1XlKyXngiaon_huxa06kLemmbYSzcWeRDtY26uD",
                " https://api.dev.insshort.com/",
                "offline.access tweet.read users.read");

        OAuth2AccessToken accessToken = null;
        try {
            final Scanner in = new Scanner(System.in, "UTF-8");
            System.out.println("Fetching the Authorization URL...");

            final String secretState = "state";
            PKCE pkce = new PKCE();
            pkce.setCodeChallenge("challenge");
            pkce.setCodeChallengeMethod(PKCECodeChallengeMethod.PLAIN);
            pkce.setCodeVerifier("challenge");
            String authorizationUrl = service.getAuthorizationUrl(pkce, secretState);

            System.out.println("Go to the Authorization URL and authorize your App:\n" +
                    authorizationUrl + "\nAfter that paste the authorization code here\n>>");
            final String code = in.nextLine();
            System.out.println("\nTrading the Authorization Code for an Access Token...");
            accessToken = service.getAccessToken(pkce, code);

            System.out.println("Access token: " + accessToken.getAccessToken());
            System.out.println("Refresh token: " + accessToken.getRefreshToken());

            HttpResponse execute = HttpRequest.get("https://api.twitter.com/2/users/me")
                    .bearerAuth(accessToken.getAccessToken()).execute();
            System.out.println(execute.body());
        } catch (Exception e) {
            System.err.println("Error while getting the access token:\n " + e);
            e.printStackTrace();
        }
        return accessToken;
    }
}
