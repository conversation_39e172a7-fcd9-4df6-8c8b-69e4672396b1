package com.shortplay.server.biz.rebate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.req.rebateReq.RebateReq;
import com.shortplay.server.manager.po.rebate.RebatePO;
import com.shortplay.server.service.rebateService.RebateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class RebateBiz {

    @Resource
    private RebateService rebateService;

    /**
     * 创建返佣配置
     */
    public CommonResult<RebatePO> create(RebateReq req) {
        try {
            RebatePO po = new RebatePO();
            BeanUtils.copyProperties(req, po);
            po.setGmtCreate(System.currentTimeMillis());
            po.setGmtModified(System.currentTimeMillis());

            boolean success = rebateService.save(po);
            if (success) {
                return CommonResult.success(po);
            } else {
                return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", "创建失败");
            }
        } catch (Exception e) {
            log.error("创建返佣配置失败", e);
            return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", e.getMessage());
        }
    }

    /**
     * 根据ID删除返佣配置
     */
    public CommonResult<Void> delete(Long id) {
        try {
            boolean success = rebateService.removeById(id);
            if (success) {
                return CommonResult.success();
            } else {
                return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", "删除失败，记录不存在");
            }
        } catch (Exception e) {
            log.error("删除返佣配置失败，id: {}", id, e);
            return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", e.getMessage());
        }
    }


    /**
     * 根据ID查询返佣配置
     */
    public CommonResult<RebatePO> getById(Long id) {
        try {
            RebatePO po = rebateService.getById(id);
            if (po == null) {
                return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", "记录不存在");
            }
            return CommonResult.success(po);
        } catch (Exception e) {
            log.error("查询返佣配置失败，id: {}", id, e);
            return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", e.getMessage());
        }
    }

    /**
     * 获取当前生效的返佣配置（最新的一条）
     */
    public CommonResult<RebatePO> getCurrent() {
        try {
            QueryWrapper<RebatePO> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("gmt_create").last("LIMIT 1");
            RebatePO po = rebateService.getOne(queryWrapper);

            if (po == null) {
                return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, null, "记录不存在");
            }
            return CommonResult.success(po);
        } catch (Exception e) {
            log.error("获取当前返佣配置失败", e);
            return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "返佣配置", e.getMessage());
        }
    }

}




