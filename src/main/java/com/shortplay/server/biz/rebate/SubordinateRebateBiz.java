package com.shortplay.server.biz.rebate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.dto.RebateRecord;
import com.shortplay.server.component.req.PageReq;
import com.shortplay.server.component.resp.rebateResp.RebateStatisticsResp;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.rebate.SubordinateRebatePO;
import com.shortplay.server.service.rebateService.SubordinateRebateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SubordinateRebateBiz {

    @Resource
    private SubordinateRebateService subordinateRebateService;

    /**
     * 分页查询下级返佣记录列表
     */
    public CommonResult<RebateStatisticsResp> list(UserInfoPO user, PageReq req) {

        // 返回参数
        Double totalCoins = 0D;
        List<RebateRecord> level1 = null;
        List<RebateRecord> level2 = null;

        try {
            // 构造查询参数
            QueryWrapper<SubordinateRebatePO> queryWrapper1 = new QueryWrapper<>();
            QueryWrapper<SubordinateRebatePO> queryWrapper2 = new QueryWrapper<>();
            queryWrapper1.eq("user_id", user.getUserId());
            queryWrapper2.eq("user_id", user.getUserId());
            queryWrapper1.eq("next_user_type", 1);
            queryWrapper2.eq("next_user_type", 2);
            queryWrapper1.orderByDesc("user_rebate_time");
            queryWrapper2.orderByDesc("user_rebate_time");
            // 查询数据库
            List<SubordinateRebatePO> level1List = subordinateRebateService.list(queryWrapper1);
            List<SubordinateRebatePO> level2List = subordinateRebateService.list(queryWrapper2);
            // 计算总返利金币数量
            totalCoins = level1List.stream().mapToDouble(SubordinateRebatePO::getUserRebate).sum() + level2List.stream().mapToDouble(SubordinateRebatePO::getUserRebate).sum();
            // 分页和转换为响应对象
            level1 = level1List.stream()
                    .skip((req.getCurrent() - 1) * req.getSize())
                    .limit(req.getSize())
                    .map(RebateRecord::PO2Resp)
                    .collect(Collectors.toList());
            level2 = level2List.stream()
                    .skip((req.getCurrent() - 1) * req.getSize())
                    .limit(req.getSize())
                    .map(RebateRecord::PO2Resp)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("分页查询下级返佣记录失败", e);
            return CommonResult.failed(BizCode.BIZ_COMMON_ERROR, "下级返佣", e.getMessage());
        }
        // 返回
        RebateStatisticsResp resp = new RebateStatisticsResp();
        resp.setTotal(totalCoins);
        resp.setLevel1(level1);
        resp.setLevel2(level2);
        return CommonResult.success(resp);
    }
}
