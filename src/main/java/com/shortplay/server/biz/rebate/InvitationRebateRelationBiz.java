package com.shortplay.server.biz.rebate;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.req.rebateReq.InvitationRebateRelationReq;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.rebate.InvitationRebateRelationPO;
import com.shortplay.server.service.UserInfoService;
import com.shortplay.server.service.rebateService.InvitationRebateRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class InvitationRebateRelationBiz {

    @Resource
    private InvitationRebateRelationService invitationRebateRelationService;

    @Resource
    private UserInfoService userInfoService;

    /**
     * 创建邀请返佣关系
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> create(InvitationRebateRelationReq req) {
        try {
            /**
             * 检查用户是否已存在邀请关系
             */
            QueryWrapper<InvitationRebateRelationPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", req.getUserId());
            InvitationRebateRelationPO existing = invitationRebateRelationService.getOne(queryWrapper);
            if (existing != null) {
                return CommonResult.failed(BizCode.CREATE_REBATE_RELATION_FAILED, false);
            }

            /**
             * 创建邀请返佣关系
             */
            InvitationRebateRelationPO po = new InvitationRebateRelationPO();
            // 1. 被邀请者的 userId
            po.setUserId(req.getUserId());
            // 2. 根据邀请码找到邀请者
            UserInfoPO inviter = userInfoService.lambdaQuery().eq(UserInfoPO::getInviteCode, req.getInviteCode()).one();
            if (inviter == null) {
                return CommonResult.failed(BizCode.INVITE_CODE_NOT_FOUND, false);
            }
            po.setMyInvitorUserId(inviter.getUserId()); // 邀请者的 userId
            po.setGmtCreate(System.currentTimeMillis()); // 创建时间
            po.setGmtModified(System.currentTimeMillis()); // 修改时间
            // 3. 如果有再上一级邀请者
            if (inviter.getIsBeInvited() != -1) {
                InvitationRebateRelationPO upperInvitePo = invitationRebateRelationService.getById(inviter.getIsBeInvited());
                if (upperInvitePo == null) {
                    throw new BizException(BizCode.DATA_NOT_EXIST, "upper invite po");
                }
                po.setMyTopInvitorUserId(upperInvitePo.getMyInvitorUserId());
                po.setLevel1Or2(2); // 二级用户
            } else {
                po.setLevel1Or2(1); // 一级用户
            }
            boolean success = invitationRebateRelationService.save(po);
            if (success) {
                // 4. 在 UserInfoPO 中记录该用户是被邀请的
                boolean update = userInfoService.lambdaUpdate().eq(UserInfoPO::getUserId, req.getUserId()).set(UserInfoPO::getIsBeInvited, po.getId()).update();
                if (!update) {
                    throw new BizException(BizCode.DATA_UPDATE_FAILED, "update user info failed");
                }
                return CommonResult.success(true);
            } else {
                throw new BizException(BizCode.CREATE_REBATE_RELATION_FAILED, "create invite relation failed");
            }
        } catch (Exception e) {
            log.error("创建邀请返佣关系失败", e);
            return CommonResult.failed(BizCode.CREATE_REBATE_RELATION_FAILED, false);
        }
    }

}

