package com.shortplay.server.biz;

import com.shortplay.server.manager.po.ConfigPO;
import com.shortplay.server.service.ConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApkBiz {
    @Autowired
    private ConfigService  configService;
    public String getApk() {
        ConfigPO configPO = configService.lambdaQuery()
                .eq(ConfigPO::getName, "apk_name")
                .one();
        String apkName = configPO.getValue();
        return "https://apk-zhongdong.s3.me-central-1.amazonaws.com/" +apkName;
    }
}
