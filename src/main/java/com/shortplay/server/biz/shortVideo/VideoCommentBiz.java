package com.shortplay.server.biz.shortVideo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.component.req.shortVideoReq.VideoCommentReq;
import com.shortplay.server.component.resp.shortVideoResp.VideoCommentResp;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.VideoInfoPO;
import com.shortplay.server.manager.po.shortVideo.UserVideoCommentRelationPO;
import com.shortplay.server.manager.po.shortVideo.VideoCommentPO;
import com.shortplay.server.service.UserVideoRelationService;
import com.shortplay.server.service.VideoInfoService;
import com.shortplay.server.service.impl.UserInfoServiceImpl;
import com.shortplay.server.service.shortVideoService.UserVideoCommentRelationService;
import com.shortplay.server.service.shortVideoService.VideoCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VideoCommentBiz {


    @Resource
    private VideoInfoService videoInfoService;

    @Resource
    private VideoCommentService videoCommentService;

    @Resource
    private UserInfoServiceImpl userInfoServiceImpl;

    @Resource
    private UserVideoCommentRelationService userVideoCommentRelationService;

    public void sendComment(UserInfoPO user, VideoCommentReq req) {
        if(req.getContent() == null) {
            throw BizException.of(BizCode.COMMENT_NOT_FOUND);
        }
        videoInfoService.lambdaQuery()
                .eq(VideoInfoPO::getVideoId, req.getVideoId())
                .oneOpt()
                .orElseThrow(() -> BizException.of(BizCode.VIDEO_NOT_FOUND));
        //获取根评论id
        Long rootId = 0L;
        Long parentId = 0L;
        if(req.getParentId() != 0){
            VideoCommentPO lastComment = videoCommentService.getById(req.getParentId());
            //判断传过来的回复id是否是同一个短视频
            if(lastComment.getVideoId().equals(req.getVideoId())){
                if(lastComment != null && lastComment.getRootId() != 0){
                    rootId = lastComment.getRootId();
                }else if(lastComment != null && lastComment.getRootId() == 0 && lastComment.getParentId() == 0){
                    rootId = lastComment.getId();
                }
                parentId = lastComment.getId();
            }else throw BizException.of(BizCode.COMMENT_NOT_FOUND);
        }

        VideoCommentPO comment = VideoCommentPO.builder()
                .videoId(req.getVideoId())
                .content(req.getContent())
                .parentId(parentId)
                .rootId(rootId)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .isDeleted(0)
                .likeCount(0L)
                .replyCount(0L)
                .uuid(user.getUuid())
                .build();

        videoCommentService.save(comment);
    }

    public Page<VideoCommentResp> getRootComment(Long videoId,int current, int size) {
        Page<VideoCommentPO> rootComment = videoCommentService.lambdaQuery()
                .eq(VideoCommentPO::getParentId, 0)
                .eq(VideoCommentPO::getRootId, 0)
                .eq(VideoCommentPO::getIsDeleted, 0)
                .eq(VideoCommentPO::getVideoId, videoId)
                .orderByDesc(VideoCommentPO::getCreateTime)
                .page(new Page<>(current,size));
        List<VideoCommentResp> commentData = rootComment.getRecords()
                .stream()
                .map(comment -> {
                    VideoCommentResp commentResp = new VideoCommentResp();
                    commentResp.setVideoCommentPO(comment);
                    userInfoServiceImpl.lambdaQuery()
                            .eq(UserInfoPO::getUuid, comment.getUuid())
                            .oneOpt()
                            .ifPresent(userInfoPO -> {
                                commentResp.setAvatarUrl(userInfoPO.getAvatar());
                                commentResp.setName(userInfoPO.getName());
                            });
                    return commentResp;
                })
                .collect(Collectors.toList());
        Page<VideoCommentResp> commentPage = new Page<>();
        commentPage.setRecords(commentData);
        commentPage.setTotal(rootComment.getTotal());
        commentPage.setSize(size);
        commentPage.setCurrent(current);
        return commentPage;
    }

    public Page<VideoCommentResp> getChildComment(Long rootId, int current , int size) {
        if(rootId != null){
            Page<VideoCommentPO> childrenComment = videoCommentService.lambdaQuery()
                    .eq(VideoCommentPO::getRootId,rootId)
                    .eq(VideoCommentPO::getIsDeleted,0)
                    .orderByDesc(VideoCommentPO::getCreateTime)
                    .page(new Page<>(current, size));
            List<VideoCommentResp> commentData = childrenComment.getRecords()
                    .stream()
                    .map(comment -> {
                        VideoCommentResp commentResp = new VideoCommentResp();
                        commentResp.setVideoCommentPO(comment);
                        userInfoServiceImpl.lambdaQuery()
                                .eq(UserInfoPO::getUuid, comment.getUuid())
                                .oneOpt()
                                .ifPresent(userInfoPO -> {
                                    commentResp.setAvatarUrl(userInfoPO.getAvatar());
                                    commentResp.setName(userInfoPO.getName());
                                });
                        return commentResp;
                    })
                    .collect(Collectors.toList());
            Page<VideoCommentResp> commentPage = new Page<>();
            commentPage.setRecords(commentData);
            commentPage.setTotal(childrenComment.getTotal());
            commentPage.setSize(size);
            commentPage.setCurrent(current);
            return commentPage;
        }
        return null;
    }

    public VideoCommentPO getVideoCommentInfo(Long commentId) {
        VideoCommentPO videoCommentPO = videoCommentService.getById(commentId);
        return videoCommentPO;
    }

    public void videoCommentLike(UserInfoPO userInfo, VideoCommentPO videoCommentInfo) {
        userVideoCommentRelationService.videoCommentLike(userInfo,videoCommentInfo);
    }

    public Long getVideoCommentLikeNum(Long commentId) {
        VideoCommentPO videoCommentPO = videoCommentService.getById(commentId);
        return videoCommentPO.getLikeCount();
    }

    public void videoCommentLikeCancel(UserInfoPO userInfo, VideoCommentPO videoCommentInfo) {
        UserVideoCommentRelationPO userVideoCommentRelationPO = userVideoCommentRelationService.lambdaQuery()
                .eq(UserVideoCommentRelationPO::getCommentId, videoCommentInfo.getId())
                .eq(UserVideoCommentRelationPO::getUuid, userInfo.getUuid())
                .one();
        if (userVideoCommentRelationPO != null) {
            userVideoCommentRelationPO.setMatchLike(false);
            userVideoCommentRelationService.updateById(userVideoCommentRelationPO);
        }
        videoCommentInfo.setLikeCount(videoCommentInfo.getLikeCount() - 1);
        videoCommentService.saveOrUpdate(videoCommentInfo);
    }
}
