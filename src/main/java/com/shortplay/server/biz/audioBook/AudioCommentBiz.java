package com.shortplay.server.biz.audioBook;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.component.req.audioBookReq.NovelCommentReq;
import com.shortplay.server.component.resp.audioBookResp.NovelCommentResp;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.audioBook.AudioNovelCommentPO;
import com.shortplay.server.manager.po.audioBook.AudioNovelInfoPO;
import com.shortplay.server.service.UserInfoService;
import com.shortplay.server.service.audioBookService.AudioNovelCommentService;
import com.shortplay.server.service.audioBookService.AudioNovelInfoService;
import com.shortplay.server.service.audioBookService.UserNovelCommentRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AudioCommentBiz {


    @Resource
    private AudioNovelInfoService audioNovelInfoService;

    @Resource
    private AudioNovelCommentService audioNovelCommentService;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private UserNovelCommentRelationService userNovelCommentRelationService;

    public void sendComment(UserInfoPO user, NovelCommentReq req) {
        if(req.getContent() == null){
            throw BizException.of(BizCode.COMMENT_NOT_FOUND);
        }
        audioNovelInfoService.lambdaQuery()
                .eq(AudioNovelInfoPO::getId, req.getNovelId())
                .oneOpt()
                .orElseThrow(()->BizException.of(BizCode.NOVEL_NOT_FOUND));

        Long rootId = 0L;
        Long parentId = 0L;
        if(req.getParentId() != 0){
            AudioNovelCommentPO lastComment = audioNovelCommentService.getById(req.getParentId());
            //判断传过来的回复id是否是同一个短视频
            if(lastComment.getNovelId().equals(req.getNovelId())) {
                if (lastComment != null && lastComment.getRootId() != 0) {
                    rootId = lastComment.getRootId();
                } else if (lastComment != null && lastComment.getRootId() == 0 && lastComment.getParentId() == 0) {
                    rootId = lastComment.getId();
                }
                parentId = lastComment.getId();
            }else throw BizException.of(BizCode.COMMENT_NOT_FOUND);
        }

        AudioNovelCommentPO audioNovelCommentPO = AudioNovelCommentPO.builder()
                .novelId(req.getNovelId())
                .userId(user.getUserId())
                .content(req.getContent())
                .parentId(parentId)
                .rootId(rootId)
                .createTime(System.currentTimeMillis())
                .isDeleted(0)
                .likeCount(0L)
                .replyCount(0L)
                .build();

        audioNovelCommentService.save(audioNovelCommentPO);
    }

    public Page<NovelCommentResp> getRootComment(int current, int size, long novelId) {
        Page<AudioNovelCommentPO> rootComment = audioNovelCommentService.lambdaQuery()
                .eq(AudioNovelCommentPO::getParentId, 0)
                .eq(AudioNovelCommentPO::getRootId, 0)
                .eq(AudioNovelCommentPO::getIsDeleted, 0)
                .eq(AudioNovelCommentPO::getNovelId, novelId)
                .orderByDesc(AudioNovelCommentPO::getCreateTime)
                .page(new Page<>(current, size));
        List<NovelCommentResp> commentData = rootComment.getRecords()
                .stream()
                .map(comment ->{
                    NovelCommentResp commentResp = new NovelCommentResp();
                    commentResp.setAudioNovelCommentPO(comment);
                    userInfoService.lambdaQuery()
                            .eq(UserInfoPO::getUserId, comment.getUserId())
                            .oneOpt()
                            .ifPresent(userInfoPO -> {
                                commentResp.setAvatar(userInfoPO.getAvatar());
                                commentResp.setName(userInfoPO.getName());
                            });
                    return commentResp;
                }).collect(Collectors.toList());
        Page<NovelCommentResp> commentPage = new Page<>();
        commentPage.setRecords(commentData);
        commentPage.setTotal(rootComment.getTotal());
        commentPage.setSize(size);
        commentPage.setCurrent(current);
        return commentPage;

    }

    public Page<NovelCommentResp> getChildComment(int current, int size, Long rootId) {
        if(rootId != null){
            Page<AudioNovelCommentPO> childrenComment = audioNovelCommentService.lambdaQuery()
                    .eq(AudioNovelCommentPO::getRootId,rootId)
                    .eq(AudioNovelCommentPO::getIsDeleted,0)
                    .orderByDesc(AudioNovelCommentPO::getCreateTime)
                    .page(new Page<>(current, size));
            List<NovelCommentResp> commentData = childrenComment.getRecords()
                    .stream()
                    .map(comment -> {
                        NovelCommentResp commentResp = new NovelCommentResp();
                        commentResp.setAudioNovelCommentPO(comment);
                        userInfoService.lambdaQuery()
                                .eq(UserInfoPO::getUserId, comment.getUserId())
                                .oneOpt()
                                .ifPresent(userInfoPO -> {
                                    commentResp.setAvatar(userInfoPO.getAvatar());
                                    commentResp.setName(userInfoPO.getName());
                                });
                        return commentResp;
                    }).collect(Collectors.toList());
            Page<NovelCommentResp> commentPage = new Page<>();
            commentPage.setRecords(commentData);
            commentPage.setTotal(childrenComment.getTotal());
            commentPage.setSize(size);
            commentPage.setCurrent(current);
            return commentPage;
        }
        return null;
    }

    public void like(UserInfoPO userInfo, AudioNovelCommentPO audioNovelCommentInfo, boolean like) {
        userNovelCommentRelationService.like(userInfo, audioNovelCommentInfo, like);
    }
}
