package com.shortplay.server.biz;

import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.component.req.CommentReq;
import com.shortplay.server.component.resp.CommentResp;
import com.shortplay.server.manager.po.ChapterInfoPO;
import com.shortplay.server.manager.po.UserCommentPO;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.service.ChapterInfoService;
import com.shortplay.server.service.UserCommentService;
import com.shortplay.server.service.impl.UserInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CommentBiz {
    @Autowired
    private UserCommentService userCommentService;
    @Autowired
    private UserInfoServiceImpl userInfoServiceImpl;
    @Autowired
    private ChapterInfoService chapterInfoService;
    @Value("${amazon.avatarUrl}")
    private String avatarUrl;

    //发评论
    public void sendComment(UserInfoPO user, CommentReq req) {
        if (req.getContent()==null){
            throw BizException.of(BizCode.COMMENT_NOT_FOUND);
        }
        chapterInfoService.lambdaQuery()
                .eq(ChapterInfoPO::getBookId,req.getBookId())
                .eq(ChapterInfoPO::getChapterId,req.getChapterId())
                .oneOpt()
                .orElseThrow(()->BizException.of(BizCode.CHAPTER_NOT_FOUND));
        //获取根评论id
        Integer rootId = 0;
        Integer parentId = 0;
        if ( req.getParentId() != 0){
            UserCommentPO lastComment = userCommentService.getById(req.getParentId());
            //判断传过来的回复id是否是同一集
            if (lastComment.getBookId() == req.getBookId() && lastComment.getChapterId() == req.getChapterId()) {
                if (lastComment != null && lastComment.getRootId() != 0) {
                    rootId = lastComment.getRootId();
                } else if (lastComment != null && lastComment.getRootId() == 0 && lastComment.getParentId() == 0) {
                    rootId = lastComment.getId();
                }
                parentId = lastComment.getId();
            }else throw BizException.of(BizCode.COMMENT_NOT_FOUND);
        }


        UserCommentPO comment = UserCommentPO.builder()
                .bookId(req.getBookId())
                .chapterId(req.getChapterId())
                .content(req.getContent())
                .rootId(rootId)
                .isDeleted(0)
                .parentId(parentId)
                .uuid(user.getUuid())
                .updateTime(System.currentTimeMillis())
                .createTime(System.currentTimeMillis())
                .build();

        userCommentService.save(comment);
    }

    //
    public List<CommentResp> getRootComment(Long bookId , Long chapterId ){

       List<UserCommentPO> rootComment = userCommentService.lambdaQuery()
                .eq(UserCommentPO::getParentId,0)
                .eq(UserCommentPO::getRootId,0)
                .eq(UserCommentPO::getIsDeleted,0)
                .eq(UserCommentPO::getBookId,bookId)
                .eq(UserCommentPO::getChapterId,chapterId)
                .orderByDesc(UserCommentPO::getCreateTime)
                .list();
       List<CommentResp> commentRespList = new ArrayList<>();
        for (UserCommentPO comment : rootComment){
            CommentResp commentResp = new CommentResp();
            commentResp.setUserCommentPO(comment);
            userInfoServiceImpl.lambdaQuery()
                    .eq(UserInfoPO::getUuid,comment.getUuid())
                    .oneOpt()
                    .ifPresent(userInfoPO -> {
                        commentResp.setAvatarUrl(avatarUrl+userInfoPO.getAvatar());
                        commentResp.setName(userInfoPO.getName());
                    });
            commentRespList.add(commentResp);
        }
        return commentRespList;
    }

    public List<CommentResp> getChildComment(Integer rootId) {
        if (rootId != null){
            List<UserCommentPO> childrenComment = userCommentService.lambdaQuery()
                    .eq(UserCommentPO::getRootId,rootId)
                    .eq(UserCommentPO::getIsDeleted,0)
                    .orderByDesc(UserCommentPO::getCreateTime)
                    .list();
            List<CommentResp> commentRespList = new ArrayList<>();
            for (UserCommentPO comment : childrenComment){
                CommentResp commentResp = new CommentResp();
                commentResp.setUserCommentPO(comment);
                userInfoServiceImpl.lambdaQuery()
                        .eq(UserInfoPO::getUuid,comment.getUuid())
                        .oneOpt()
                        .ifPresent(userInfoPO -> {
                            commentResp.setAvatarUrl(userInfoPO.getAvatar());
                            commentResp.setName(userInfoPO.getName());
                        });
                commentRespList.add(commentResp);
            }
            return commentRespList;
        }
        return  null;
    }
}
