package com.shortplay.server.component.dto;

import com.shortplay.server.manager.po.rebate.SubordinateRebatePO;
import lombok.Data;

import java.util.Date;

@Data
public class RebateRecord {

    /**
     * 下级用户昵称
     */
    private String nextUserName;

    /**
     * 返利金币数
     */
    private Double coins;

    /**
     * 返利时间
     */
    private Long rebateTime;

    public static RebateRecord PO2Resp(SubordinateRebatePO po) {
        RebateRecord record = new RebateRecord();
        record.setNextUserName(po.getNextUserName());
        record.setCoins(po.getUserRebate());
        record.setRebateTime(po.getUserRebateTime());
        return record;
    }
}
