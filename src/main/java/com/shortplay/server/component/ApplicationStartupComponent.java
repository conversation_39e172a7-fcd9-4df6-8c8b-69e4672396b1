package com.shortplay.server.component;

import com.shortplay.server.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class ApplicationStartupComponent {
    @Resource
    private ConfigService configService;

    @EventListener(ApplicationReadyEvent.class)
    public void doSomethingAfterStartup() {
        // check mongo config
        System.out.println("Spring Boot 应用启动完成，检查配置...");
        configService.initConfig();
        System.out.println("Spring Boot 应用启动完成，检查结束...");
    }
}
