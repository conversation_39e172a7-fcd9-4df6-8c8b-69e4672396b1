package com.shortplay.server.component.enums;

public enum AppleProductEnum {

    GOLD_500("95682434", "500gold coins", "500 金币"),
    GOLD_1000("963852141", "1000gold coins", "1000 金币"),
    GOLD_1500("9638521410", "1500gold coins", "1500 金币"),
    GOLD_2500("963852148", "2500gold coins", "2500 金币"),
    GOLD_5000("963852149", "5000gold coins", "5000 金币"),
    ANNUAL_PASS("763256", "Annual Pass", "年度通行证"),
    ZHOU_KA("9456982", "<PERSON> Ka", "周卡");

    private final String productId;
    private final String referenceName;
    private final String displayName;

    AppleProductEnum(String productId, String referenceName, String displayName) {
        this.productId = productId;
        this.referenceName = referenceName;
        this.displayName = displayName;
    }

    public String getProductId() {
        return productId;
    }

    public String getReferenceName() {
        return referenceName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * 根据 productId 获取对应枚举
     */
    public static AppleProductEnum fromProductId(String productId) {
        for (AppleProductEnum e : values()) {
            if (e.productId.equals(productId)) {
                return e;
            }
        }
        return null;
    }
}