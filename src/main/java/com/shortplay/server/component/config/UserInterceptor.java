package com.shortplay.server.component.config;

import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWTUtil;
import com.google.api.client.http.HttpMethods;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

@Slf4j
public class UserInterceptor implements HandlerInterceptor {

    public static final String SECRET_KEY = "XbHcRlvNk1pbxGarEES1mgo4THtByvgStxHa075w5xms2ELdG9";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (HttpMethods.OPTIONS.equals(request.getMethod())) {
            return true;
        }
        // 检查Cookie
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("adminauthorization".equals(cookie.getName())) {
                    // 这里应包含你的逻辑来验证Cookie值的合法性
                    String cookieValue = cookie.getValue();
                    try {
                        if (JWTUtil.verify(cookieValue, SECRET_KEY.getBytes(StandardCharsets.UTF_8))) {
                            return true;
                        }
                    } catch (Exception e) {
                        log.error("authorization verify error", e);
                    }
                }
            }
        }
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        // Cookie不合法或不存在，不放行
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        PrintWriter writer = response.getWriter();
        writer.write(JSONUtil.toJsonStr(CommonResult.failed(BizCode.LOGIN_ERROR)));
        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }
}
