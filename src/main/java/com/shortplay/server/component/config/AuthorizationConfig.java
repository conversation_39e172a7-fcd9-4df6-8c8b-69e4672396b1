package com.shortplay.server.component.config;

import com.google.api.client.googleapis.auth.oauth2.GoogleClientSecrets;
import com.twitter.clientlib.auth.TwitterOAuth20Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * google登录授权配置
 */
@Configuration
public class AuthorizationConfig {

    @Value("${authorization.google.clientId}")
    private String googleClientId;

    @Value("${authorization.google.clientSecret}")
    private String googleClientSecret;

    @Value("${authorization.twitter.clientId}")
    private String twitterClientId;

    @Value("${authorization.twitter.clientSecret}")
    private String twitterClientSecret;

    @Value("${authorization.twitter.callback}")
    private String twitterCallback;


    @Bean
    public GoogleClientSecrets googleClientSecrets() {
        GoogleClientSecrets clientSecrets = new GoogleClientSecrets();
        GoogleClientSecrets.Details details = new GoogleClientSecrets.Details();
        details.setClientId(googleClientId);
        details.setClientSecret(googleClientSecret);
        clientSecrets.setInstalled(details);
        return clientSecrets;
    }

    @Bean
    public TwitterOAuth20Service twitterOAuth20Service() {
        return new TwitterOAuth20Service(
                twitterClientId,
                twitterClientSecret,
                twitterCallback,
                "offline.access tweet.read users.read");
    }

}