package com.shortplay.server.component.config;


import com.paypal.base.Constants;
import com.paypal.base.rest.APIContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PaypalConfig {

    /**
     * 开发模式 sandbox 正式模式 live
     */
    @Value("${paypal.mode}")
    private String paypalMode;

    @Value("${paypal.clientId}")
    private String clientId;

    @Value("${paypal.clientSecret}")
    private String clientSecret;

    @Value("${paypal.webhookId}")
    private String webhookId;

    @Bean
    public APIContext getApiContext() {
        APIContext apiContext = new APIContext(clientId, clientSecret, paypalMode);
        apiContext.addConfiguration(Constants.PAYPAL_WEBHOOK_ID, webhookId);
        return apiContext;
    }
}
