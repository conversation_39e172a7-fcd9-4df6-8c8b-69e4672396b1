package com.shortplay.server.component.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Knife4j API文档配置
 */
@Configuration
public class Knife4jConfig {

    @Value("${server.servlet.context-path:/}")
    private String contextPath;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * 全局OpenAPI配置
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("StartV Backend API")
                        .description("StartV短剧平台后端API接口文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("StartV Team")
                                .email("<EMAIL>")))
                .servers(List.of(
                        new Server().url("http://localhost:" + serverPort + contextPath).description("本地开发环境")
                ));
    }
}
