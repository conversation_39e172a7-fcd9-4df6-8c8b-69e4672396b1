package com.shortplay.server.component.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 支付成功注解
 * 用于标记支付成功的方法，触发返利逻辑
 * 在所有支付成功的方法上添加该注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PaymentSuccess {
    
    /**
     * 支付类型描述
     */
    String paymentType() default "";
    
    /**
     * 是否启用返利
     */
    boolean enableRebate() default true;
}
