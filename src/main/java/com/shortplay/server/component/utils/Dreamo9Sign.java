package com.shortplay.server.component.utils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Map;

public class Dreamo9Sign {
    public static String getSign(Map<String, Object> map, String key) {
        ArrayList<String> list = new ArrayList();
        Iterator var3 = map.entrySet().iterator();

        while(var3.hasNext()) {
            Map.Entry<String, Object> entry = (Map.Entry)var3.next();
            if (null != entry.getValue() && !"".equals(entry.getValue())) {
                list.add((String)entry.getKey() + "=" + entry.getValue() + "&");
            }
        }

        int size = list.size();
        String[] arrayToSort = (String[])list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();

        for(int i = 0; i < size; ++i) {
            sb.append(arrayToSort[i]);
        }

        String result = sb.toString();
        result = result + "key=" + key;
        result = md5(result, "UTF-8").toUpperCase();
        return result;
    }

    public static String md5(String value, String charset) {
        MessageDigest md = null;

        try {
            byte[] data = value.getBytes(charset);
            md = MessageDigest.getInstance("MD5");
            byte[] digestData = md.digest(data);
            return toHex(digestData);
        } catch (NoSuchAlgorithmException var5) {
            var5.printStackTrace();
            return null;
        } catch (UnsupportedEncodingException var6) {
            var6.printStackTrace();
            return null;
        }
    }

    public static String toHex(byte[] input) {
        if (input == null) {
            return null;
        } else {
            StringBuffer output = new StringBuffer(input.length * 2);

            for(int i = 0; i < input.length; ++i) {
                int current = input[i] & 255;
                if (current < 16) {
                    output.append("0");
                }

                output.append(Integer.toString(current, 16));
            }

            return output.toString();
        }
    }

}
