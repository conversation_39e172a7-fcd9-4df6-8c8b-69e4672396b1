package com.shortplay.server.component.utils;

import com.google.analytics.data.v1beta.BetaAnalyticsDataClient;
import com.google.analytics.data.v1beta.BetaAnalyticsDataSettings;
import com.google.analytics.data.v1beta.DateRange;
import com.google.analytics.data.v1beta.Dimension;
import com.google.analytics.data.v1beta.Metric;
import com.google.analytics.data.v1beta.Row;
import com.google.analytics.data.v1beta.RunReportRequest;
import com.google.analytics.data.v1beta.RunReportResponse;
import com.google.auth.oauth2.GoogleCredentials;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


import java.io.FileInputStream;
@Component
public class QuickstartSample {

    private static  String propertyId = "488842634";
    private static String keyFilePath = "/usr/myproject/tonal-feat-456608-g8-7ce2394a06bb.json";
    public static  Integer sampleRunReport() throws Exception {

        // 直接用代码加载服务账号 JSON
       // String keyFilePath = "C:\\Users\\<USER>\\Desktop\\tonal-feat-456608-g8-7ce2394a06bb.json";

//        // 设置代理
//        System.setProperty("https.proxyHost", "127.0.0.1");
//        System.setProperty("https.proxyPort", "7890");

        GoogleCredentials credentials;
        try (FileInputStream serviceAccountStream = new FileInputStream(keyFilePath)) {
            credentials = GoogleCredentials.fromStream(serviceAccountStream);
        }

        BetaAnalyticsDataSettings settings =
                BetaAnalyticsDataSettings.newBuilder()
                        .setCredentialsProvider(() -> credentials)
                        .build();
        int count = 0;
        try (BetaAnalyticsDataClient analyticsData = BetaAnalyticsDataClient.create(settings)) {
            RunReportRequest request =
                    RunReportRequest.newBuilder()
                            .setProperty("properties/" + propertyId)
                            .addDimensions(Dimension.newBuilder().setName("city"))
                            //.addMetrics(Metric.newBuilder().setName("activeUsers"))
                            .addMetrics(Metric.newBuilder().setName("screenPageViews"))  // 浏览次数
                            .addDateRanges(DateRange.newBuilder().setStartDate("2025-05-14").setEndDate("today"))
                            .build();

            RunReportResponse response = analyticsData.runReport(request);

            System.out.println("Report result:");
            for (Row row : response.getRowsList()) {
                System.out.printf(
                        "%s, %s%n", row.getDimensionValues(0).getValue(), row.getMetricValues(0).getValue());

                count += Integer.parseInt(row.getMetricValues(0).getValue());
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }
}
