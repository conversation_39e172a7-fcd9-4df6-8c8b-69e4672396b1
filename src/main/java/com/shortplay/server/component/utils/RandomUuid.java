package com.shortplay.server.component.utils;

import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.service.UserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;


@Component
public class RandomUuid {

    @Autowired
    UserInfoService userInfoService;
    public static Integer getRandomUuid() {
        int number = ThreadLocalRandom.current().nextInt(1_000_000, 10_000_000); // 范围：[1000000, 9999999]
        return number+10000000;
    }

}
