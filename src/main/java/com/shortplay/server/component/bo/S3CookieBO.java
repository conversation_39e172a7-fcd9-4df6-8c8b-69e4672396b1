package com.shortplay.server.component.bo;

import com.google.common.collect.Maps;
import com.shortplay.server.component.common.Constants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/1/9
 **/
@Data
public class S3CookieBO {
    //Set-Cookie: CloudFront-Expires=1426500000; Domain=d111111abcdef8.cloudfront.net; Path=/images/*; Secure; HttpOnly
    private Map<String, String> cookieMap;

    private String keyPairId;
    //Set-Cookie: CloudFront-Signature=yXrSIgyQoeE4FBI4eMKF6ho~CA8_; Domain=d111111abcdef8.cloudfront.net; Path=/images/*; Secure; HttpOnly
    private String signature;
    //Set-Cookie: CloudFront-Key-Pair-Id=K2JCJMDEHXQW5F; Domain=d111111abcdef8.cloudfront.net; Path=/images/*; Secure; HttpOnly
    private String expires;

    private String policy;

    public Map<String, String> getCookieMap() {
        if (Objects.nonNull(cookieMap)) {
            return cookieMap;
        }
        cookieMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(signature)) {
            int signatureIndex = signature.indexOf(Constants.EQUAL_SIGN);
            cookieMap.put(signature.substring(0, signatureIndex), signature.substring(signatureIndex + 1));
        }
        if (StringUtils.isNotBlank(policy)) {
            int policyIndex = policy.indexOf(Constants.EQUAL_SIGN);
            cookieMap.put(policy.substring(0, policyIndex), policy.substring(policyIndex + 1));
        }
        if (StringUtils.isNotBlank(keyPairId)) {
            int keyPairIdIndex = keyPairId.indexOf(Constants.EQUAL_SIGN);
            cookieMap.put(keyPairId.substring(0, keyPairIdIndex), keyPairId.substring(keyPairIdIndex + 1));
        }
        if (StringUtils.isNotBlank(expires)) {
            int expiresIndex = expires.indexOf(Constants.EQUAL_SIGN);
            cookieMap.put(expires.substring(0, expiresIndex), expires.substring(expiresIndex + 1));
        }
        return cookieMap;
    }
}
