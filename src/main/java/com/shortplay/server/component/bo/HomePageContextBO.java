package com.shortplay.server.component.bo;

import com.shortplay.server.component.resp.BookInfoResp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/1/11
 **/
@Data
public class HomePageContextBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, List<BookInfoResp>> typeBookMap;

    //配置
    private List<BookInfoResp> bannerBookList;

    //保留20个
    private List<BookInfoResp> popularBookList;

    private Map<Long, BookInfoResp> idMap;
}
