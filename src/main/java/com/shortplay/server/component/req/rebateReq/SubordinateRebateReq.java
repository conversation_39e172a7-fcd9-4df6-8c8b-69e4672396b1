package com.shortplay.server.component.req.rebateReq;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 下级返佣请求对象
 */
@Data
public class SubordinateRebateReq {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 下级用户ID
     */
    @NotNull(message = "下级用户ID不能为空")
    private Long nextUserId;

    /**
     * 下级用户类型：1-一级用户，2-二级用户
     */
    @NotNull(message = "下级用户类型不能为空")
    private Integer nextUserType;

    /**
     * 该下级用户一次充值给我的返佣金币数
     */
    @NotNull(message = "返佣金币数不能为空")
    private Double userRebate;

    /**
     * 该下级用户这次充值的时间
     */
    @NotNull(message = "充值时间不能为空")
    private Long userRebateTime;
}
