package com.shortplay.server.component.req.admin;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/4/14
 **/
@Data
@Valid
public class UpdateBookReq {
    @NotNull
    private Long bookId;

    private String title;

    private String arTitle;

    private String type;

    private String brief;

    private String arBrief;

    private Long collectNum;

    private Long startLockChapterId;

    private Integer isVipUnlock;

    private Boolean isOnline;

    private Integer sort;
}
