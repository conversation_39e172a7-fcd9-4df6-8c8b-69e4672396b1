package com.shortplay.server.component.req;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class PageReq {
    /**
     * 当前页码，从1开始
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 是否升序，默认false（降序）
     */
    private Boolean asc = false;


}
