package com.shortplay.server.component.req.admin;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/4/14
 **/
@Data
@Valid
public class UpdateChapterReq {
    @NotNull
    private Long bookId;
    @NotNull
    private Long chapterId;

    private Long likeNum;
    private Long collectNum;
    private Long visitNum;
    private Long costNum;
}
