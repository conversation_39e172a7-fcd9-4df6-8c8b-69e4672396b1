package com.shortplay.server.component.req.admin;
import lombok.Data;

@Data
public class PayNotifyRequest {

    private String payOrderId;     // 支付订单号
    private String mchNo;          // 商户号
    private String appId;          // 应用ID
    private String mchOrderNo;     // 商户订单号
    private Integer amount;        // 支付金额（分）
    private String currency;       // 货币代码
    private Integer state;         // 订单支付状态
    private Integer payAmount;     // 实际支付金额（可选，只有BDT）
    private String errCode;        // 错误码（可选）
    private String errMsg;         // 错误描述（可选）
    private String customerName;   // 客户姓名（可选）
    private String customerEmail;  // 客户邮箱（可选）
    private String customerPhone;  // 客户手机号（可选）
    private String extParam;       // 商户扩展参数（可选）
    private Long createdAt;        // 订单创建时间
    private Long reqTime;          // 请求接口时间
    private String sign;           // 签名
    private String channelOrderNo; // 渠道订单号（可选）
    private String clientIp;       // 客户端IP（可选）

}
