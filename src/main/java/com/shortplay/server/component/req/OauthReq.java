package com.shortplay.server.component.req;

import com.shortplay.server.component.enums.OauthTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023/12/17
 **/
@Data
public class OauthReq {

    /**
     * 登录的类型 {@link OauthTypeEnum#values()}
     */
    @NotBlank
    private String type;

    /**
     * 回调时的code，回调时使用，非必填
     */
    private String code;
    /**
     * 回调时的 url，可选
     */
    private String redirectUrl;
}
