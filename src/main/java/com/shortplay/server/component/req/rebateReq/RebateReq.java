package com.shortplay.server.component.req.rebateReq;

import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 返佣配置请求对象
 */
@Data
public class RebateReq {
    
    /**
     * 一级返佣比例
     */
    @NotNull(message = "一级返佣比例不能为空")
    @DecimalMin(value = "0.00", message = "一级返佣比例不能小于0")
    @DecimalMax(value = "1.00", message = "一级返佣比例不能大于1")
    private BigDecimal level1RebateRate;

    /**
     * 二级返佣比例
     */
    @NotNull(message = "二级返佣比例不能为空")
    @DecimalMin(value = "0.00", message = "二级返佣比例不能小于0")
    @DecimalMax(value = "1.00", message = "二级返佣比例不能大于1")
    private BigDecimal level2RebateRate;
}
