package com.shortplay.server.component.resp.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
@Builder
public class CostResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消费的金币数
     */
    private Long coinsNum;

    /**
     * 系列id
     */
    private Long bookId;

    /**
     * 章节id
     */
    private Long chapterId;

    /**
     * 消费时间
     */
    private Long payTime;

}
