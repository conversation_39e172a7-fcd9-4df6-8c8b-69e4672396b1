package com.shortplay.server.component.resp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/14
 **/
@Data
public class UserInfoContextResp {
    /**
     * 用户id
     */
    private String userId;

    /**
     * 已观看列表
     */
    private List<BookInfoResp> watchBookList;

    /**
     * 已收集列表
     */
    private List<ChapterCollectResp> collectList;

    /**
     * 金币数量
     */
    private Long coins;

    /**
     * 积分数量
     */
    private Long bonus;

    /**
     * 时区
     */
    private int tzOffset;

    /**
     * 国家
     */
    private String country;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 昵称
     */
    private String name;

    private Integer uuid;

    private Integer vip;

    private Long vipTime;

    /**
     * 交易历史
     */
    private List<TransactionResp> TransactionRespList;

    /**
     * 解锁历史
     */
    private List<UnlockHistoryResp> unlockHistoryList;

}
