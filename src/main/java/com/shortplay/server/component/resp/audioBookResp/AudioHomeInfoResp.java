package com.shortplay.server.component.resp.audioBookResp;


import cn.hutool.core.bean.BeanUtil;
import com.shortplay.server.manager.po.audioBook.AudioNovelInfoPO;
import com.shortplay.server.manager.po.audioBook.UserNovelRelationPO;
import lombok.Data;

@Data
public class AudioHomeInfoResp {

    /**
     * 有声书id
     */
    private Long id;

    /**
     * 有声书标题
     */
    private String title;

    /**
     * 作者id
     */
    private String authorId;

    /**
     * 封面图URL
     */
    private String coverUrl;

    /**
     * 有声书详细描述
     */
    private String description;

    /**
     * 状态（draft/serializing/completed）
     */
    private String status;

    /**
     * 总时长（秒）
     */
    private int totalDuration;


    public static AudioHomeInfoResp PO2Resp(AudioNovelInfoPO audioNovelInfoPO) {
        AudioHomeInfoResp audioHomeInfoResp = new AudioHomeInfoResp();
        BeanUtil.copyProperties(audioNovelInfoPO, audioHomeInfoResp);
        return audioHomeInfoResp;
    }

    public static AudioHomeInfoResp ArPO2Resp(AudioNovelInfoPO audioNovelInfoPO) {
        AudioHomeInfoResp audioHomeInfoResp = new AudioHomeInfoResp();
        BeanUtil.copyProperties(audioNovelInfoPO, audioHomeInfoResp);
        audioHomeInfoResp.setTitle(audioNovelInfoPO.getArTitle());
        audioHomeInfoResp.setDescription(audioNovelInfoPO.getArDescription());
        return audioHomeInfoResp;
    }
}
