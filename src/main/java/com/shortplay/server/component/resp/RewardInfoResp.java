package com.shortplay.server.component.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * Brandon
 **/
@Data
public class RewardInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;
    private ShareInfo shareInfo;
    private DailySignInfo dailySignInfo;
    private boolean addHomeScreen;
    private Long totalBonus;


    @Data
    public static class ShareInfo {
        private int facebook;
        private int instagram;
        private int twitter;
        private int tiktok;
        private int whatsapp;
    }

    @Data
    public static class DailySignInfo {
        /**
         * 已签到天数
         */
        private int signDays;

        /**
         * 今天是否可签到
         */
        private boolean signAvailable;
    }
}
