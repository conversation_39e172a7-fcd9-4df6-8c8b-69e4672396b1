package com.shortplay.server.component.resp.shortVideoResp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoShareResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享次数
     */
    private long shareCount;

    /**
     * 分享渠道
     */
    private String channel;
}
