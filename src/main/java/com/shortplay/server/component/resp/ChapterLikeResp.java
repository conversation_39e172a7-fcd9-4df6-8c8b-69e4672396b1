package com.shortplay.server.component.resp;

import com.shortplay.server.manager.po.UserChapterRelationPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/7/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChapterLikeResp implements Serializable {
    /**
     * 剧集系列id
     */
    private long bookId;

    /**
     * 剧集系列名称
     */
    private String title;

    /**
     * 剧集系列封面图
     */
    private String fontUrl;

    /**
     * 剧集id
     */
    private long chapterId;

    /**
     * 剧集总集数
     */
    private long allNum;

    /**
     * 点赞时间
     */
    private long updateTime;

    public static ChapterLikeResp copy(UserChapterRelationPO relationPO) {
        return ChapterLikeResp.builder()
                .bookId(relationPO.getBookId())
                .chapterId(relationPO.getChapterId())
                .fontUrl("")
                .title("")
                .allNum(0L)
                .updateTime(relationPO.getUpdateTime())
                .build();
    }
}
