package com.shortplay.server.component.resp.audioBookResp;


import cn.hutool.core.bean.BeanUtil;
import com.shortplay.server.manager.po.audioBook.AudioNovelInfoPO;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class NovelDetailResp {

    /**
     * 小说id
     */
    private Long id;

    /**
     * 小说标题
     */
    private String title;

    /**
     * 作者id
     */
    private String authorId;

    /**
     * 封面图URL
     */
    private String coverUrl;

    /**
     * 有声书详细描述
     */
    private String description;

    /**
     * 状态（draft/serializing/completed）
     */
    private String status;

    private boolean isVip;

    private BigDecimal price;

    private Long playCount;

    private Long collectCount;

    private Long commentCount;

    private Long likeCount;

    private Long createTime;

    private Long updateTime;

    private boolean matchCollect;

    private boolean matchLike;

    private String authorAvatar;

    private Long shareCount;

    private int chapterCount;

    public static NovelDetailResp PO2Resp(AudioNovelInfoPO audioNovelInfoPO) {
        NovelDetailResp novelDetailResp = new NovelDetailResp();
        BeanUtil.copyProperties(audioNovelInfoPO, novelDetailResp);
        return novelDetailResp;
    }

    public static NovelDetailResp ArPO2Resp(AudioNovelInfoPO audioNovelInfoPO) {
        NovelDetailResp novelDetailResp = new NovelDetailResp();
        BeanUtil.copyProperties(audioNovelInfoPO, novelDetailResp);
        novelDetailResp.setTitle(audioNovelInfoPO.getArTitle());
        novelDetailResp.setDescription(audioNovelInfoPO.getArDescription());
        return novelDetailResp;
    }
}
