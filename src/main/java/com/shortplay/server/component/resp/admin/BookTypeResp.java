package com.shortplay.server.component.resp.admin;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
@Builder
public class BookTypeResp implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 标题
     */
    private String name;

    private String arName;

    private Long count;
    private Long createTime;
    private Long updateTime;
}
