package com.shortplay.server.component.resp.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
@Builder
public class BookIncomeResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系列id
     */
    private Long bookId;

    /**
     * 短剧总收入
     */
    private double moneyTotal;

    /**
     * 系列名称
     */
    private String title;

}
