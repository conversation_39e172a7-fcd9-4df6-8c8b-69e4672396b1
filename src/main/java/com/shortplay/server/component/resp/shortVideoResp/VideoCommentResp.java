package com.shortplay.server.component.resp.shortVideoResp;

import com.shortplay.server.manager.po.shortVideo.VideoCommentPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoCommentResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 短视频评论信息
     */
    private VideoCommentPO videoCommentPO;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 昵称
     */
    private String name;
}
