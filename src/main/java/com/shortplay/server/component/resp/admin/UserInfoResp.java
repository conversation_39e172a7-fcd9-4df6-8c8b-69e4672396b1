package com.shortplay.server.component.resp.admin;


import com.shortplay.server.component.resp.TransactionResp;
import com.shortplay.server.component.resp.UnlockHistoryResp;
import com.shortplay.server.manager.po.UserInfoPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
public class UserInfoResp {

    private static final long serialVersionUID = 1L;

    private String userId;

    private Integer vip;

    private String userType;

    private String avatar;

    private String name;

    private Long coins;

    private Long vipTime;

    private String countryName;

    private String stateProvince;

    private String registryIP;

    private int tzOffset;

    public static UserInfoResp copy(UserInfoPO userInfoPO) {
        return UserInfoResp.builder()
                .userId(userInfoPO.getUserId())
                .vip(userInfoPO.getVip())
                .userType(userInfoPO.getUserType())
                .avatar(userInfoPO.getAvatar())
                .name(userInfoPO.getName())
                .coins(userInfoPO.getCoins())
                .vipTime(userInfoPO.getVipTime())
                .countryName(userInfoPO.getCountryName())
                .stateProvince(userInfoPO.getStateProvince())
                .registryIP(userInfoPO.getRegistryIP())
                .tzOffset(userInfoPO.getTzOffset())
                .build();
    }

}
