package com.shortplay.server.component.resp;

import lombok.Data;

import java.io.Serializable;

@Data
public class AdWatchResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 观看广告获得的积分数
     */
    private Integer bonusEarned;

    /**
     * 观看广告次数
     */
    private Integer adWatchCount;

    /**
     * 是否显示订阅弹窗
     */
    private Boolean showSubscriptionPopup;
}
