package com.shortplay.server.component.resp;

import cn.hutool.json.JSONUtil;
import com.shortplay.server.manager.po.GoodsInfoPO;
import com.shortplay.server.manager.po.GoodsOrderPO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/1/14
 **/
@Data
public class TransactionResp {

    /**
     * 支付金额，USD计价
     */
    private String money;

    /**
     * 支付方式 credit_card, paypal
     */
    private String payType;

    /**
     * 完成时间
     */
    private Long payDoneTime;

    /**
     * 获取金币的购买数
     */
    private Long coinsNum;

    /**
     * 获取金币的奖励数
     */
    private Long bonusNum;

    public static TransactionResp buildTransactionRespByPO(GoodsOrderPO po) {
        GoodsInfoPO goodsInfoPO = JSONUtil.parse(po.getGoodsDetail()).toBean(GoodsInfoPO.class);

        TransactionResp resp = new TransactionResp();
        resp.setMoney(po.getMoney().toString());
        resp.setCoinsNum(goodsInfoPO.getCoinNum());
        resp.setBonusNum(goodsInfoPO.getBonusNum());
        resp.setPayType(po.getPayType());
        resp.setPayDoneTime(po.getPayDoneTime());
        return resp;
    }
}
