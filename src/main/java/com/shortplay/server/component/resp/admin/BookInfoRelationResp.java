package com.shortplay.server.component.resp.admin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BookInfoRelationResp implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long relationId;

    private Integer order;

    /**
     * 唯一键
     */
    private Long bookId;

    /**
     * 类型
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 简介
     */
    private String brief;

    /**
     * 封面
     */
    private String fontUrl;

    /**
     * 封面
     */
    private String bannerUrl;

    /**
     * 收藏数量
     */
    private Long collectNum;

    /**
     * 已完成集数
     */
    @JsonIgnore
    private Long completeNum;


    /**
     * 总集数
     */
    private Long allNum;

    /**
     * 需要解锁的集数
     */
    private Long startLockChapterId;

    /**
     * 是否在线
     */
    private boolean matchOnline;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;
}
