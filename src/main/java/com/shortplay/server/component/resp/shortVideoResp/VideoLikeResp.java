package com.shortplay.server.component.resp.shortVideoResp;


import com.shortplay.server.manager.po.UserVideoRelationPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoLikeResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短视频id
     */
    private Long videoId;

    /**
     * 点赞数量
     */
    private Long likeNum;

    /**
     * 封面URL
     */
    private String fontUrl;

    /**
     * 点赞时间
     */
    private Long updateTime;

    /**
     * 短视频标题
     */
    private String title;

    public static VideoLikeResp copy(UserVideoRelationPO relationPO) {
        return VideoLikeResp.builder()
                .videoId(relationPO.getVideoId())
                .likeNum(0L)
                .fontUrl("")
                .updateTime(relationPO.getUpdateTime())
                .title("")
                .build();
    }
}
