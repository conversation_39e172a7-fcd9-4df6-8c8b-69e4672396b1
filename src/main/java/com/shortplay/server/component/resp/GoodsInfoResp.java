package com.shortplay.server.component.resp;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.paypal.api.payments.Currency;
import com.shortplay.server.component.enums.PayPalCurrencyEnum;
import com.shortplay.server.manager.po.GoodsInfoPO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/1/26
 **/
@Data
public class GoodsInfoResp {
    /**
     * 唯一键
     */
    private Long goodId;

    /**
     * 商品价格，美元计价，单位 ：分
     */
    private Long priceUsdCent;

    /**
     * vip商品价格，美元计价，单位 ：分
     */
    private Long vipPriceUsdCent;

    /**
     * 转换后的货币计价，分
     */
    private Long priceCent;

    /**
     * 货币类型
     */
    private PayPalCurrencyEnum currency;

    /**
     * 奖励数量
     */
    private Long coinNum;

    /**
     * 积分数量
     */
    private Long bonusNum;

    /**
     * 是否为首次充值 banner
     */
    private boolean isBanner;

    /**
     * 是否推荐标志
     */
    private boolean best;

    /**
     * 商品类型
     */
    private Integer goodType;

    /**
     * vip时间
     */
    private Long vipTime;


    public static GoodsInfoResp PO2Resp(GoodsInfoPO goodsInfoPO) {
        GoodsInfoResp goodsInfoResp = new GoodsInfoResp();
        BeanUtil.copyProperties(goodsInfoPO, goodsInfoResp);
        goodsInfoResp.setBanner(goodsInfoPO.matchBanner());
        goodsInfoResp.setPriceCent(goodsInfoResp.getPriceUsdCent());
        if (goodsInfoPO.getGoodType() == 0) {
            goodsInfoResp.setVipPriceUsdCent((long) (goodsInfoPO.getPriceUsdCent() * 0.8));
        }
        goodsInfoResp.setCurrency(PayPalCurrencyEnum.AED);
        return goodsInfoResp;
    }
}
