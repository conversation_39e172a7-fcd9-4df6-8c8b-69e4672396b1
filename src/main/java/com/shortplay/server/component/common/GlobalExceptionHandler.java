package com.shortplay.server.component.common;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 异常处理拦截器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常处理
     */
    @ExceptionHandler(BizException.class)
    public CommonResult<Void> bizExceptionHandler(BizException e) {
        log.info("Catch bizException. {}", ExceptionUtil.getMessage(e));
        return CommonResult.failed(e.code, e.message);
    }

    /**
     * Handler param not valid exception
     *
     * @param e MethodArgumentNotValidException
     * @return Encapsulated ApiResult
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult<Object> bizExceptionHandler(MethodArgumentNotValidException e) {
        log.error("Catch param not valid exception. {}", ExceptionUtil.stacktraceToString(e));
        List<ObjectError> objectErrorList = e.getBindingResult().getAllErrors();
        if (CollectionUtils.isEmpty(objectErrorList)) {
            return CommonResult.failed(BizCode.PARAMETERS_INVALID);
        }
        log.error("objectErrorList:{}", JSONUtil.toJsonStr(objectErrorList));
        List<String> messageList = objectErrorList.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        return CommonResult.failedWithData(BizCode.PARAMETERS_INVALID, messageList);
    }

    /**
     * Handler request method not supported exception
     *
     * @param e HttpRequestMethodNotSupportedException
     * @return Encapsulated ApiResult
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public CommonResult<Object> bizExceptionHandler(HttpRequestMethodNotSupportedException e) {
        log.error("Request method not supported. {}", ExceptionUtil.stacktraceToString(e));
        return CommonResult.failed(BizCode.PARAMETER_REQUEST_METHOD_NOT_SUPPORT);
    }

    /**
     * 服务异常处理
     */
    @ExceptionHandler(Exception.class)
    public CommonResult<Void> exceptionHandler(HttpServletResponse resp, Exception e) {
        log.error("Catch server exception. {}", ExceptionUtil.stacktraceToString(e));
        resp.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return CommonResult.failed(BizCode.UNKNOWN_ERROR.code, e.getMessage());
    }
}
