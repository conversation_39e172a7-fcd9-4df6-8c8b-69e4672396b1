package com.shortplay.server.component.common;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.shortplay.server.component.bo.S3CookieBO;
import com.shortplay.server.service.impl.AmazonService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 对请求S3的信息进行验证
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class S3CookieHandler implements ResponseBodyAdvice<Object> {

    @Value("${amazon.cookieDomain}")
    private String cookieDomain;
    @Resource
    private AmazonService amazonService;
    @Override
    public boolean supports(@NotNull MethodParameter returnType,
                            @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType,
                                  @NotNull MediaType selectedContentType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {
        try {
            ServletServerHttpRequest servletServerHttpRequest = (ServletServerHttpRequest) request;
            ServletServerHttpResponse servletServerHttpResponse = (ServletServerHttpResponse) response;

            Cookie[] cookies = servletServerHttpRequest.getServletRequest().getCookies();
            JSON parse = JSONUtil.parse(JSONUtil.toJsonStr(body));
            CommonResult<?> result = parse.toBean(CommonResult.class);
            // 管理后台也需要看图片，所以也要 cookie
            // 只处理 非 以"/api/admin"开头的URL
//            if (servletServerHttpRequest.getURI().getPath().startsWith("/api/admin")) {
//                return body; // 不是我们关注的URL，直接放行
//            }
            // 返回异常结果不进行设置
            if (!result.code.equals(BizCode.OK.code)) {
                return body;
            }
            Set<String> reqCookie = Arrays.stream(Optional.ofNullable(cookies).orElse(new Cookie[]{})).map(Cookie::getName).collect(Collectors.toSet());
            // 如果已经设置则不需要设置
            if (reqCookie.contains(Constants.COOKIE_ESSENTIAL_KEY)) {
                return body;
            }
            setCookie(servletServerHttpResponse);
        } catch (Exception e) {
            log.error("setCookie error, error is {}", ExceptionUtil.stacktraceToString(e));
        }
        return body;
    }

    private void setCookie(ServletServerHttpResponse response) {
        S3CookieBO cookieBO = amazonService.getCookieByPolicy("*", 7);
        // 创建一个 cookie对象
        Map<String, String> cookieMap = cookieBO.getCookieMap();
        for (String key : cookieMap.keySet()) {
            Cookie cookie = new Cookie(key, cookieMap.get(key));
            //Https 安全cookie
            cookie.setSecure(true);
            cookie.setDomain(cookieDomain);
            cookie.setPath("/");
            //设置三天失效
            cookie.setMaxAge((int) TimeUnit.DAYS.toSeconds(3));
            //将cookie对象加入response响应
            response.getServletResponse().addCookie(cookie);
        }
    }
}