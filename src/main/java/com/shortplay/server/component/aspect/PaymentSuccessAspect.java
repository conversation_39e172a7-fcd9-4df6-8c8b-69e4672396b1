package com.shortplay.server.component.aspect;

import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shortplay.server.component.annotation.PaymentSuccess;
import com.shortplay.server.component.enums.AppleProductEnum;
import com.shortplay.server.component.event.PaymentSuccessEvent;
import com.shortplay.server.manager.po.GoodsOrderPO;
import com.shortplay.server.manager.po.UserInfoPO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Base64;

/**
 * 支付成功切面
 * 拦截带有@PaymentSuccess注解的方法，发布支付成功事件
 */
@Aspect
@Component
@Slf4j
public class PaymentSuccessAspect {

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 在支付成功方法执行后触发
     */
    @AfterReturning("@annotation(paymentSuccess)")
    public void handlePaymentSuccess(JoinPoint joinPoint, PaymentSuccess paymentSuccess) {
        try {
            // 检查是否启用返利
            if (!paymentSuccess.enableRebate()) {
                log.info("返利功能已禁用，跳过返利处理");
                return;
            }

            // 获取方法参数
            // Paypal支付成功后执行的方法的参数
            Object[] args = joinPoint.getArgs();
            GoodsOrderPO goodsOrder = null;
            UserInfoPO userInfo = null;
            // 苹果支付成功后执行的方法的参数 需要的部分
            String receipt = null;
            String userId = null;

            // 获取参数名称
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] paramNames = signature.getParameterNames();

            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (arg instanceof GoodsOrderPO) {
                    goodsOrder = (GoodsOrderPO) arg;
                } else if (arg instanceof UserInfoPO) {
                    userInfo = (UserInfoPO) arg;
                } else if (arg instanceof String) {
                    String paramName = paramNames[i];
                    if ("receipt".equals(paramName)) {
                        receipt = (String) arg;
                    } else if ("userId".equals(paramName)) {
                        userId = (String) arg;
                    }
                }
            }

            // 从参数中提取订单和用户信息
            // 根据不同的支付方式处理
            if (goodsOrder != null && userInfo != null) { // PayPal支付处理
                Long coin = 0L;
                // todo 获取该订单金币数量
                if (goodsOrder.getMoney() == 0.99) {
                    coin = 500L;
                } else if (goodsOrder.getMoney() == 1.99) {
                    coin = 1000L;
                } else if (goodsOrder.getMoney() == 2.99) {
                    coin = 1500L;
                } else if (goodsOrder.getMoney() == 4.99) {
                    coin = 2500L;
                } else if (goodsOrder.getMoney() == 9.99) {
                    coin = 5000L;
                }
                // 发布支付成功事件
                PaymentSuccessEvent event = new PaymentSuccessEvent(
                        this,
                        userInfo.getUserId(),  // 用户ID
                        BigDecimal.valueOf(goodsOrder.getMoney()), // 支付金额，USD计价
                        coin,
                        paymentSuccess.paymentType(), // 支付方式 credit_card, paypal
                        goodsOrder.getOrderId(), // 商品订单号,唯一键
                        goodsOrder.getPayDoneTime() // 完成时间
                );

                eventPublisher.publishEvent(event);

                log.info("支付成功事件已发布: userId={}, amount={}, orderId={}",
                        userInfo.getUserId(), goodsOrder.getMoney(), goodsOrder.getOrderId());
            } else if (receipt != null && userId != null) { // 苹果支付处理
                String[] parts = receipt.split("\\.");
                if (parts.length != 3) {
                    log.error("无效的JWS字符串，必须包含3部分:", receipt);
                }
                String payloadBase64Url = parts[1];
                // 1. Base64URL 解码payload
                byte[] decodedPayload = Base64.getUrlDecoder().decode(payloadBase64Url);
                String payloadJson = new String(decodedPayload, "UTF-8");
                // 2. 反序列化为Java对象
                ObjectMapper mapper = new ObjectMapper();
                JWSTransactionDecodedPayload payload = mapper.readValue(payloadJson, JWSTransactionDecodedPayload.class);
                if (payload != null && payload.getProductId() != null) {
                    // 发布支付成功事件
                    // 解析加金币
                    AppleProductEnum appleProductEnum = AppleProductEnum.fromProductId(payload.getProductId());
                    Long coin = 0L;
                    switch (appleProductEnum.name()) {
                        case "GOLD_500":
                            coin = 500L * payload.getQuantity();
                            break;
                        case "GOLD_1000":
                            coin = 1000L * payload.getQuantity();
                            break;
                        case "GOLD_1500":
                            coin = 1500L * payload.getQuantity();
                            break;
                        case "GOLD_2500":
                            coin = 2500L * payload.getQuantity();
                            break;
                        case "GOLD_5000":
                            coin = 5000L * payload.getQuantity();
                            break;
                        default:
                    }
                    PaymentSuccessEvent event = new PaymentSuccessEvent(
                            this,
                            userId,  // 用户ID
                            coin, // 金币数
                            "ios", // 支付方式
                            payload.getTransactionId(), // 商品订单号,唯一键
                            payload.getPurchaseDate() // 完成时间
                    );
                    eventPublisher.publishEvent(event);
                    log.info("ios支付成功事件已发布: userId={}, amount={}, transactionId={}",
                            userId, coin, payload.getTransactionId());
                } else {
                    log.error("内购购买，解析的数据有错误 解析的内容:", payload);
                }
            } else {
                log.warn("无法从方法参数中获取订单或用户信息，跳过返利处理");
                return;
            }
        } catch (Exception e) {
            log.error("处理支付成功事件时发生错误", e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}
