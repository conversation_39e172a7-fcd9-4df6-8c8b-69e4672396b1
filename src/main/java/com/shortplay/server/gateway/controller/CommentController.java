package com.shortplay.server.gateway.controller;

import com.shortplay.server.biz.CommentBiz;
import com.shortplay.server.biz.LoginBiz;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.req.CommentReq;
import com.shortplay.server.component.resp.CommentResp;
import com.shortplay.server.manager.po.UserCommentPO;
import com.shortplay.server.manager.po.UserInfoPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/comment")
@Slf4j
public class CommentController {
    @Autowired
    private LoginBiz loginBiz;
    @Autowired
    private CommentBiz commentBiz;
    @Value("${amazon.avatarUrl}")
    private String avatarUrl;

    /**
     * 发送评论
     * 功能：用户对指定章节发表评论，支持游客和注册用户
     * 实现逻辑：
     * 1. 验证用户登录状态（可选，游客也可评论）
     * 2. 验证评论内容的格式和长度限制
     * 3. 进行敏感词过滤和内容审核
     * 4. 创建评论记录，包含用户信息、章节信息、评论内容
     * 5. 更新章节的评论数量统计
     * 6. 可能触发评论相关的奖励机制
     *
     * @param authorization 登录凭证，可选参数（游客可评论）
     * @param req 评论请求对象，包含章节ID、评论内容等信息
     * @return 无返回数据，成功则返回 success
     */
    @PostMapping("/send")
    public CommonResult sendComment(@RequestHeader(required = false) String authorization, @RequestBody CommentReq req) {
        UserInfoPO user = null;
        //查询用户信息
        if (StringUtils.isNotBlank(authorization)) {
            user = loginBiz.getUserByTokenAndVerify(authorization);
        }
        commentBiz.sendComment(user, req);

        return CommonResult.success();
    }

    /**
     * 获取根评论列表
     * 功能：获取指定章节的所有根评论（非回复评论）
     * 实现逻辑：
     * 1. 根据剧集ID和章节ID查询所有根评论
     * 2. 只返回顶级评论，不包含回复评论
     * 3. 按评论时间或热度排序
     * 4. 包含评论者的基本信息（昵称、头像等）
     * 5. 返回评论内容、点赞数、回复数等统计信息
     *
     * @param bookId 剧集ID
     * @param chapterId 章节ID
     * @return 返回根评论列表
     */
    @GetMapping("/getRootComment")
    public CommonResult<List<CommentResp>> getRootComment(Long bookId , Long chapterId ) {

        List<CommentResp> rootComment = commentBiz.getRootComment(bookId,chapterId);
        return CommonResult.success(rootComment);
    }

    /**
     * 获取子评论列表
     * 功能：获取指定根评论下的所有回复评论
     * 实现逻辑：
     * 1. 根据根评论ID查询所有回复评论
     * 2. 按回复时间顺序排列
     * 3. 包含回复者的基本信息
     * 4. 支持多级回复的展示
     * 5. 可能包含@用户的功能
     *
     * @param rootId 根评论ID
     * @return 返回该根评论下的所有回复评论列表
     */
    @GetMapping("/getChildComment")
    public CommonResult<List<CommentResp>> getChildComment(Integer rootId) {
        List<CommentResp> childComment = commentBiz.getChildComment(rootId);
        return CommonResult.success(childComment);
    }

}
