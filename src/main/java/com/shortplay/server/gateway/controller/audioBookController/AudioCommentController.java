package com.shortplay.server.gateway.controller.audioBookController;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.shortplay.server.biz.LoginBiz;
import com.shortplay.server.biz.audioBook.AudioCommentBiz;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.req.audioBookReq.NovelCommentReq;
import com.shortplay.server.component.resp.audioBookResp.NovelCommentResp;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.audioBook.AudioNovelCommentPO;
import com.shortplay.server.service.audioBookService.AudioNovelCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

@RestController
@RequestMapping("/audio")
@Slf4j
public class AudioCommentController {

    @Resource
    private LoginBiz loginBiz;
    @Resource
    private AudioCommentBiz audioCommentBiz;
    @Resource
    private AudioNovelCommentService audioNovelCommentService;

    /**
     * 发送有声书小说评论
     * @param authorization
     * @param req
     * @return
     */
    @PostMapping("/send")
    public CommonResult sendComment(@RequestHeader(required = false) String authorization,
                                    @RequestBody NovelCommentReq req){
        UserInfoPO user = null;
        if(StringUtils.isNotBlank(authorization)){
            user = loginBiz.getUserByTokenAndVerify(authorization);
        }
        audioCommentBiz.sendComment(user,req);
        return CommonResult.success();
    }

    /**
     * 获取有声书根评论
     * @param novelId
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/novel/getRootComment")
    public CommonResult getRootComment(@RequestParam int novelId,
                                       @RequestParam int current,
                                       @RequestParam int size){
        Page<NovelCommentResp> rootComment = audioCommentBiz.getRootComment(current,size,novelId);
        return CommonResult.success(rootComment);
    }

    /**
     * 获取有声书子评论
     * @param rootId
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/novel/getChildComment")
    public CommonResult getChildComment(@RequestParam Long rootId,
                                        @RequestParam int current,
                                        @RequestParam int size){
        Page<NovelCommentResp> childComment = audioCommentBiz.getChildComment(current,size,rootId);
        return CommonResult.success(childComment);
    }

    /**
     * 点赞评论
     * @param authorization
     * @param commentId
     * @return
     */
    @PostMapping("/novel/comment/like")
    public CommonResult<Long> like(@RequestHeader String authorization,
                                   @RequestParam Long commentId){
        if(Objects.isNull(commentId)){
            throw BizException.of(BizCode.PARAMETER_INVALID, "commentId");
        }
        AudioNovelCommentPO audioNovelCommentInfo = audioNovelCommentService.getById(commentId);
        if(audioNovelCommentInfo == null){
            return CommonResult.failed(BizCode.COMMENT_NOT_FOUND);
        }
        UserInfoPO userInfo = null;
        if(StringUtils.isNotBlank(authorization)){
            userInfo = loginBiz.getUserByTokenAndVerify(authorization);
        }
        audioCommentBiz.like(userInfo, audioNovelCommentInfo,true);
        Long likeNum = audioNovelCommentInfo.getLikeCount();
        return CommonResult.success(likeNum);
    }

    /**
     * 取消评论点赞
     * @param authorization
     * @param commentId
     * @return
     */
    @PostMapping("/novel/comment/likeCancel")
    public CommonResult<Long> likeCancel(@RequestHeader String authorization,
                                         @RequestParam Long commentId){
        if(Objects.isNull(commentId)){
            throw BizException.of(BizCode.PARAMETER_INVALID, "commentId");
        }
        AudioNovelCommentPO audioNovelCommentInfo = audioNovelCommentService.getById(commentId);
        if(audioNovelCommentInfo == null){
            return CommonResult.failed(BizCode.COMMENT_NOT_FOUND);
        }
        UserInfoPO userInfo = null;
        if(StringUtils.isNotBlank(authorization)){
            userInfo = loginBiz.getUserByTokenAndVerify(authorization);
        }
        audioCommentBiz.like(userInfo, audioNovelCommentInfo,false);
        Long likeNum = audioNovelCommentInfo.getLikeCount();
        return CommonResult.success(likeNum);
    }
}
