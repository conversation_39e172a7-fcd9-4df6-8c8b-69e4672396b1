package com.shortplay.server.gateway.controller.rebateController;

import com.shortplay.server.biz.rebate.RebateBiz;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.req.rebateReq.RebateReq;
import com.shortplay.server.manager.po.rebate.RebatePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 返佣配置控制器
 */
@RestController
@RequestMapping("/rebate/config")
@Slf4j
public class RebateController {

    @Resource
    private RebateBiz rebateBiz;

    /**
     * 创建返佣配置
     */
    @PostMapping("/create")
    public CommonResult<RebatePO> create(@Valid @RequestBody RebateReq req) {
        return rebateBiz.create(req);
    }

    /**
     * 根据ID删除返佣配置
     */
    @DeleteMapping("/delete/{id}")
    public CommonResult<Void> delete(@PathVariable Long id) {
        return rebateBiz.delete(id);
    }


    /**
     * 根据ID查询返佣配置
     */
    @GetMapping("/get/{id}")
    public CommonResult<RebatePO> getById(@PathVariable Long id) {
        return rebateBiz.getById(id);
    }

    /**
     * 获取当前生效的返佣配置（最新的一条）
     */
    @GetMapping("/getCurrent")
    public CommonResult<RebatePO> getCurrent() {
        return rebateBiz.getCurrent();
    }

}
