package com.shortplay.server.gateway.controller;

import com.shortplay.server.biz.AdminBiz;
import com.shortplay.server.biz.ApkBiz;
import com.shortplay.server.component.common.CommonResult;
import com.shortplay.server.component.req.BookReq;
import com.shortplay.server.component.resp.ChapterListResp;
import com.shortplay.server.component.utils.QrCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/apk")
@Slf4j
public class ApkController {
    @Autowired
    private ApkBiz apkBiz;

    /**
     * 获取APK下载信息
     * 功能：获取最新版本APK的下载链接和对应的二维码
     * 实现逻辑：
     * 1. 调用 ApkBiz 获取最新APK文件的下载URL
     * 2. 使用 QrCodeUtil 生成下载链接的二维码（300x300像素）
     * 3. 将二维码转换为Base64编码字符串
     * 4. 返回包含下载URL和二维码的响应对象
     * 5. 用于用户扫码下载或直接下载最新版本应用
     *
     * @return 返回包含APK下载URL和二维码Base64编码的Map对象
     * @throws Exception 二维码生成或文件处理异常
     */
    @GetMapping("/getApk")
    private CommonResult getApk() throws Exception {
      String url=  apkBiz.getApk();
        String qrCodeBase64 = QrCodeUtil.generateQRCodeBase64(url,300,300);

        return CommonResult.success( Map.of(
                "apkUrl", url,
                "qrCode", qrCodeBase64
        ));
    }
}

