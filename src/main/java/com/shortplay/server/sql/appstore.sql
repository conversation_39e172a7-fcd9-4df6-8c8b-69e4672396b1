CREATE TABLE `apple_store_record` (
                                         `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
                                         `raw_type` VARCHAR(255) DEFAULT NULL COMMENT '原始类型',
                                         `raw_revocation_reason` VARCHAR(255) DEFAULT NULL COMMENT '原始撤销原因',
                                         `raw_transaction_reason` VARCHAR(255) DEFAULT NULL COMMENT '原始交易原因',
                                         `raw_offer_discount_type` VARCHAR(255) DEFAULT NULL COMMENT '原始折扣类型',
                                         `raw_in_app_ownership_type` VARCHAR(255) DEFAULT NULL COMMENT '原始拥有类型',
                                         `raw_environment` VARCHAR(255) DEFAULT NULL COMMENT '原始环境',
                                         `raw_offer_type` VARCHAR(255) DEFAULT NULL COMMENT '原始优惠类型',
                                         `unknown_fields` TEXT DEFAULT NULL COMMENT '未知字段JSON',
                                         `original_transaction_id` VARCHAR(255) NOT NULL COMMENT '原始交易ID;原始购买交易的交易标识符。',
                                         `transaction_id` VARCHAR(255) NOT NULL COMMENT '交易ID;交易的唯一标识符',
                                         `web_order_line_item_id` VARCHAR(255) DEFAULT NULL COMMENT 'Web订单项目ID;跨设备订阅购买事件的唯一标识符，包括订阅续订',
                                         `bundle_id` VARCHAR(255) NOT NULL COMMENT '应用包名',
                                         `product_id` VARCHAR(255) NOT NULL COMMENT '商品ID;商品的唯一标识码',
                                         `subscription_group_identifier` VARCHAR(255) DEFAULT NULL COMMENT '订阅组标识;订阅所属的订阅组的标识符',
                                         `purchase_date` BIGINT NOT NULL COMMENT '购买时间（时间戳）;App Store 在失效后向客户帐户收取购买、恢复产品、订阅或订阅续订费用的 UNIX 时间（以毫秒为单位）',
                                         `original_purchase_date` BIGINT NOT NULL COMMENT '原始购买时间（时间戳）;UNIX 时间（以毫秒为单位），表示原始交易标识符的购买日期',
                                         `expires_date` BIGINT DEFAULT NULL COMMENT '过期时间（时间戳）;订阅过期或续订的 UNIX 时间（以毫秒为单位）',
                                         `quantity` INT DEFAULT 1 COMMENT '购买数量;买家购买的消费品数量',
                                         `type` VARCHAR(255) DEFAULT NULL COMMENT '类型，如 Consumable;应用内购买的类型',
                                         `app_account_token` VARCHAR(255) DEFAULT NULL COMMENT '应用账户标识;您在购买时创建的 UUID，用于将交易与您自己的服务上的客户相关联。如果您的应用程序未提供应用程序帐户令牌，则省略此字段。更多信息',
                                         `in_app_ownership_type` VARCHAR(255) DEFAULT NULL COMMENT '用户拥有类型;用于描述交易是客户购买的，还是通过“家人共享”提供给他们',
                                         `signed_date` BIGINT NOT NULL COMMENT '签名时间（时间戳）;App Store 对 JSON Web 签名 （JWS） 数据进行签名的 UNIX 时间（以毫秒为单位）',
                                         `revocation_reason` VARCHAR(255) DEFAULT NULL COMMENT '撤销原因;App Store 退还交易或从家人共享中撤销交易的原因',
                                         `revocation_date` BIGINT DEFAULT NULL COMMENT '撤销时间（时间戳）;App Store 为交易退款或从“家人共享”中撤销交易的 UNIX 时间（以毫秒为单位）',
                                         `is_upgraded` BOOLEAN DEFAULT NULL COMMENT '是否升级订阅;一个布尔值，指示客户是否升级到其他订阅',
                                         `offer_type` VARCHAR(255) DEFAULT NULL COMMENT '优惠类型;表示促销优惠类型的值。',
                                         `offer_identifier` VARCHAR(255) DEFAULT NULL COMMENT '优惠标识符;包含优惠代码或促销优惠标识符的标识符',
                                         `environment` VARCHAR(255) DEFAULT NULL COMMENT '环境，如Sandbox或Production',
                                         `storefront` VARCHAR(255) DEFAULT NULL COMMENT '商店前端区域;三个字母的代码，表示与购买的 App Store 店面关联的国家或地区',
                                         `storefront_id` VARCHAR(255) DEFAULT NULL COMMENT '商店ID;一个 Apple 定义的值，用于唯一标识与购买关联的 App Store 店面',
                                         `transaction_reason` VARCHAR(255) DEFAULT NULL COMMENT '交易原因;购买交易的原因，指示是客户的购买还是系统启动的自动续订订阅的续订',
                                         `price` INT DEFAULT 0 COMMENT '价格（以最小货币单位，例如分）;一个整数值，表示价格乘以您在 App Store Connect 中配置的 App 内购买或订阅优惠的 1000，并在购买时系统记录。有关更多信息，请参阅价格。currency 参数指示此价格的货币',
                                         `currency` VARCHAR(255) DEFAULT NULL COMMENT '货币单位，如HKD',
                                         `offer_discount_type` VARCHAR(255) DEFAULT NULL COMMENT '优惠折扣类型;您为订阅优惠配置的付款方式，例如 Free Trial、Pay As You Go 或 Pay Up Front',
                                         `app_transaction_id` VARCHAR(255) DEFAULT NULL COMMENT 'App交易ID',
                                         `offer_period` VARCHAR(255) DEFAULT NULL COMMENT '优惠周期;应用于交易的选件的持续时间',
                                         `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

                                         UNIQUE KEY `uk_transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='苹果内购的购买记录';