SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tbl_comment_like
-- ----------------------------
DROP TABLE IF EXISTS `tbl_comment_like`;
CREATE TABLE `tbl_comment_like` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `comment_id` BIGINT NOT NULL COMMENT '评论唯一标识',
  `user_id` BIGINT NOT NULL COMMENT '用户唯一标识',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_comment_user` (`comment_id`, `user_id`) USING BTREE COMMENT '确保用户对评论不重复点赞',
  INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '优化按用户查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_video_share
-- ----------------------------
DROP TABLE IF EXISTS `tbl_video_share`;
CREATE TABLE `tbl_video_share` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `video_id` BIGINT NOT NULL COMMENT '短视频唯一标识',
  `user_id` BIGINT NOT NULL COMMENT '用户唯一标识',
  `channel` VARCHAR(50) NOT NULL COMMENT '分享渠道，如facebook、twitter',
  `share_url` VARCHAR(255) NOT NULL COMMENT '分享链接',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_video_id` (`video_id`) USING BTREE COMMENT '优化按视频查询',
  INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '优化按用户查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_gift_purchase
-- ----------------------------
DROP TABLE IF EXISTS `tbl_gift_purchase`;
CREATE TABLE `tbl_gift_purchase` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` BIGINT NOT NULL COMMENT '用户唯一标识',
  `gift_id` BIGINT NOT NULL COMMENT '礼物唯一标识',
  `buy_count` INTEGER NOT NULL COMMENT '购买数量',
  `total_cost` DECIMAL(10,2) NOT NULL COMMENT '总花费',
  `pay_type` VARCHAR(50) NOT NULL COMMENT '支付方式，如balance、paypal',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '优化按用户查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_video_reward
-- ----------------------------
DROP TABLE IF EXISTS `tbl_video_reward`;
CREATE TABLE `tbl_video_reward` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `video_id` BIGINT NOT NULL COMMENT '短视频唯一标识',
  `user_id` BIGINT NOT NULL COMMENT '用户唯一标识',
  `gift_id` BIGINT NOT NULL COMMENT '礼物唯一标识',
  `gift_count` INTEGER NOT NULL COMMENT '礼物数量',
  `total_amount` DECIMAL(10,2) NOT NULL COMMENT '打赏总金额',
  `message` TEXT COMMENT '打赏留言',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_video_id` (`video_id`) USING BTREE COMMENT '优化按视频查询',
  INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '优化按用户查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_user_follow
-- ----------------------------
DROP TABLE IF EXISTS `tbl_user_follow`;
CREATE TABLE `tbl_user_follow` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` BIGINT NOT NULL COMMENT '用户唯一标识',
  `kol_id` BIGINT NOT NULL COMMENT 'KOL唯一标识',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_kol` (`user_id`, `kol_id`) USING BTREE COMMENT '确保用户不重复关注KOL',
  INDEX `idx_kol_id` (`kol_id`) USING BTREE COMMENT '优化按KOL查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_private_message
-- ----------------------------
DROP TABLE IF EXISTS `tbl_private_message`;
CREATE TABLE `tbl_private_message` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `from_user_id` BIGINT NOT NULL COMMENT '发送者唯一标识',
  `to_user_id` BIGINT NOT NULL COMMENT '接收者唯一标识',
  `content_type` ENUM('text', 'voice', 'emoji', 'image', 'gift', 'call') NOT NULL COMMENT '消息类型',
  `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '消息内容',
  `ar_content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '消息阿拉伯内容',
  `duration` INTEGER COMMENT '语音/通话时长（秒）',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_from_user_id` (`from_user_id`) USING BTREE COMMENT '优化按发送者查询',
  INDEX `idx_to_user_id` (`to_user_id`) USING BTREE COMMENT '优化按接收者查询',
  INDEX `idx_create_time` (`create_time`) USING BTREE COMMENT '优化按时间查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_voice_call
-- ----------------------------
DROP TABLE IF EXISTS `tbl_voice_call`;
CREATE TABLE `tbl_voice_call` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `call_id` VARCHAR(50) NOT NULL COMMENT '通话唯一标识',
  `from_user_id` BIGINT NOT NULL COMMENT '发起者唯一标识',
  `to_user_id` BIGINT NOT NULL COMMENT '接收者唯一标识',
  `message_id` BIGINT COMMENT '关联私信唯一标识',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_call_id` (`call_id`) USING BTREE COMMENT '确保通话ID唯一',
  INDEX `idx_from_user_id` (`from_user_id`) USING BTREE COMMENT '优化按发起者查询',
  INDEX `idx_to_user_id` (`to_user_id`) USING BTREE COMMENT '优化按接收者查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tbl_user_payment
-- ----------------------------
DROP TABLE IF EXISTS `tbl_user_payment`;
CREATE TABLE `tbl_user_payment` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` BIGINT NOT NULL COMMENT '用户唯一标识',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '充值金额',
  `pay_type` VARCHAR(50) NOT NULL COMMENT '支付方式',
  `create_time` BIGINT NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '优化按用户查询'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;