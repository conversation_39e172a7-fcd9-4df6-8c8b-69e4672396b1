-- 1. 用户体系相关表

use short_play_phase_2;

-- kol表
CREATE TABLE IF NOT EXISTS `kol` (
                         `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键，用户唯一标识',
                         `user_id` VARCHAR(50) NOT NULL COMMENT 'tbl_user_info 表的自增id',
                         `balance` DECIMAL(12,2) DEFAULT 0.00 COMMENT '用户虚拟货币余额，单位：元',
                         `status` TINYINT DEFAULT 1 COMMENT '账号状态：0-禁用 1-正常 2-临时封禁',
                         `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                         `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
                         PRIMARY KEY (`id`),
                         UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='kol表';


-- 关注和粉丝关系表
CREATE TABLE IF NOT EXISTS `follow_relation` (
                             `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                             `user_id_of_fan` BIGINT NOT NULL COMMENT '粉丝用户ID，关联users表的自增id',
                             `user_id_be_followed` BIGINT NOT NULL COMMENT '被关注用户ID，关联users表的自增id',
                             `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关short_play_phase_2注时间',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `idx_user_id` (`user_id_of_fan`,`user_id_be_followed`) COMMENT '确保用户不重复关注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关注和粉丝关系表';


use short_play_phase_2;

-- 2. 直播核心表
-- 直播间表(live_rooms)
CREATE TABLE IF NOT EXISTS `live_rooms` (
                              `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键，房间唯一ID',
                              `zego_room_id` VARCHAR(100) NOT NULL COMMENT 'ZEGOCLOUD房间ID，用于音视频通信',
                              `user_id` BIGINT NOT NULL COMMENT '主播用户ID，关联users表',
                              `title` VARCHAR(100) NOT NULL COMMENT '直播间标题',
                              `cover_url` VARCHAR(255) COMMENT '封面图URL，存储在AWS S3',
                              `status` ENUM('preparing','living','finished') DEFAULT 'preparing' COMMENT '房间状态：准备中/直播中/已结束',
                              `start_time` DATETIME COMMENT '实际开始时间',
                              `end_time` DATETIME COMMENT '结束时间',
                              `online_count` INT DEFAULT 0 COMMENT '当前在线人数，每5分钟更新',
                              `total_viewers` INT DEFAULT 0 COMMENT '累计观看人数',
                              `stream_url` VARCHAR(255) COMMENT 'RTMP拉流地址，由ZEGO生成',
                              `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `idx_zego_room` (`zego_room_id`) COMMENT 'ZEGO房间ID唯一索引',
                              KEY `idx_user` (`user_id`) COMMENT '主播用户ID索引',
                              KEY `idx_status` (`status`) COMMENT '状态查询索引'
) ENGINE=InnoDB COMMENT='直播间基本信息表';

-- 观众观看记录表(room_viewers)
CREATE TABLE IF NOT EXISTS `room_viewers` (
                                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                `room_id` BIGINT NOT NULL COMMENT '直播间ID，关联live_rooms表',
                                `user_id` BIGINT NOT NULL COMMENT '观众用户ID，关联users表',
                                `enter_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '进入房间时间',
                                `leave_time` DATETIME COMMENT '离开房间时间',
                                `duration` INT DEFAULT 0 COMMENT '观看时长(秒)，离开时计算',
                                `last_active` DATETIME COMMENT '最后活跃时间，用于判断是否在线',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `idx_room_user` (`room_id`,`user_id`) COMMENT '房间+用户唯一索引',
                                KEY `idx_room` (`room_id`) COMMENT '房间查询索引',
                                KEY `idx_user` (`user_id`) COMMENT '用户查询索引',
                                KEY `idx_active` (`last_active`) COMMENT '活跃时间索引'
) ENGINE=InnoDB COMMENT='观众观看记录表，用于统计和留存分析';


-- 3. 互动功能表
-- 礼物表(gifts)
CREATE TABLE IF NOT EXISTS `gifts` (
                         `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '礼物ID',
                         `name` VARCHAR(50) NOT NULL COMMENT '礼物名称',
                         `price` DECIMAL(8,2) NOT NULL COMMENT '礼物价格(元)',
                         `icon_url` VARCHAR(255) COMMENT '礼物图标URL',
                         `animation_type` VARCHAR(50) COMMENT '前端动画类型标识',
                         `weight` INT DEFAULT 0 COMMENT '排序权重，值越大越靠前',
                         `status` TINYINT DEFAULT 1 COMMENT '状态：0-下架 1-上架',
                         `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`),
                         KEY `idx_status` (`status`) COMMENT '状态索引',
                         KEY `idx_weight` (`weight`) COMMENT '排序权重索引'
) ENGINE=InnoDB COMMENT='礼物配置表，管理员可后台管理';


-- 礼物交易表(gift_transactions)
CREATE TABLE IF NOT EXISTS `gift_transactions` (
                                     `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '交易记录ID',
                                     `room_id` BIGINT NOT NULL COMMENT '直播间ID，关联live_rooms表',
                                     `sender_id` BIGINT NOT NULL COMMENT '发送者用户ID',
                                     `receiver_id` BIGINT NOT NULL COMMENT '接收者(主播)用户ID',
                                     `gift_id` BIGINT NOT NULL COMMENT '礼物ID，关联gifts表',
                                     `amount` DECIMAL(10,2) NOT NULL COMMENT '礼物金额',
                                     `transaction_no` VARCHAR(64) COMMENT '交易流水号，用于对账',
                                     `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_room` (`room_id`) COMMENT '直播间查询索引',
                                     KEY `idx_sender` (`sender_id`) COMMENT '发送者索引',
                                     KEY `idx_receiver` (`receiver_id`) COMMENT '接收者索引',
                                     KEY `idx_created` (`created_at`) COMMENT '时间查询索引',
                                     UNIQUE KEY `idx_trans_no` (`transaction_no`) COMMENT '交易号唯一索引'
) ENGINE=InnoDB COMMENT='礼物打赏交易记录表';
-- `quantity` INT DEFAULT 1 COMMENT '礼物数量',
-- `total_amount` DECIMAL(10,2) NOT NULL COMMENT '总金额(单价×数量)',
-- `message` VARCHAR(255) COMMENT '附加留言',
-- 弹幕表(bullet_comments)
CREATE TABLE IF NOT EXISTS `bullet_comments` (
                          `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '弹幕ID',
                          `room_id` BIGINT NOT NULL COMMENT '直播间ID，关联live_rooms表',
                          `user_id` BIGINT NOT NULL COMMENT '发送用户ID，关联users表',
                          `content` VARCHAR(255) NOT NULL COMMENT '弹幕内容，已过滤敏感词',
                          `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          PRIMARY KEY (`id`),
                          KEY `idx_room` (`room_id`) COMMENT '直播间查询索引',
                          KEY `idx_user` (`user_id`) COMMENT '用户查询索引',
                          KEY `idx_created` (`created_at`) COMMENT '时间查询索引'
) ENGINE=InnoDB COMMENT='直播间弹幕记录表';
-- `color` VARCHAR(20) DEFAULT '#FFFFFF' COMMENT '弹幕颜色，十六进制',
-- `position` TINYINT DEFAULT 0 COMMENT '显示位置：0-滚动 1-顶部 2-底部',
-- `status` TINYINT DEFAULT 1 COMMENT '状态：0-审核拒绝 1-正常 2-用户删除',

-- 4. PK功能表
-- PK对战表(pk_battles)
CREATE TABLE IF NOT EXISTS `pk_battles` (
                              `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'PK对战ID',
                              `room_id1` BIGINT NOT NULL COMMENT '直播间1 ID',
                              `room_id2` BIGINT NOT NULL COMMENT '直播间2 ID',
                              `start_time` DATETIME NOT NULL COMMENT 'PK开始时间',
                              `end_time` DATETIME COMMENT 'PK结束时间',
                              `status` ENUM('pending','ongoing','finished') DEFAULT 'pending' COMMENT 'PK状态',
                              `score1` INT DEFAULT 0 COMMENT '直播间1得分',
                              `score2` INT DEFAULT 0 COMMENT '直播间2得分',
                              `winner` BIGINT COMMENT '获胜直播间ID',
                              `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              KEY `idx_room1` (`room_id1`) COMMENT '直播间1索引',
                              KEY `idx_room2` (`room_id2`) COMMENT '直播间2索引',
                              KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB COMMENT='直播间PK对战记录表';