use short_play_phase_2;

-- 为 tbl_user_info 表添加 is_be_invited 字段
ALTER TABLE `tbl_user_info`
    ADD COLUMN `is_be_invited` bigint NOT NULL DEFAULT '-1' COMMENT '是否被邀请：-1 没被邀请，否则为邀请返佣关系表自增id',
    ADD INDEX `idx_is_be_invited` (`is_be_invited`);

-- 邀请返佣关系表
CREATE TABLE if not exists `invitation_rebate_relation` (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                              `user_id` bigint NOT NULL COMMENT '用户ID，关联user表自增id',
                                              `level_1_or_2` tinyint(4) NOT NULL COMMENT '被邀请级别：1-被无上级用户邀请，是该用户的一级用户，2-被一级用户邀请，是该一级用户的上级的二级用户',
                                              `my_invitor_user_id` bigint NOT NULL COMMENT '我的邀请人的user表自增id',
                                              `my_top_invitor_user_id` bigint DEFAULT NULL COMMENT '我的最高级邀请人的user表自增id（当level_1_or_2=2时有效）',
                                              `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请返佣关系表';

-- 下级及返佣表
CREATE TABLE if not exists `subordinate_rebate` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                      `user_id` bigint NOT NULL COMMENT '用户ID，关联user表自增id',
                                      `next_user_id` bigint NOT NULL COMMENT '我的某个一级/二级用户在user表的自增id',
                                      `next_user_type` tinyint(4) NOT NULL COMMENT '这个一级/二级用户的类型 1 一级用户 2 二级用户',
                                      `user_rebate` decimal NOT NULL DEFAULT '0' COMMENT '这个一级/二级用户一次充值给我的返佣金币数',
                                      `user_rebate_time` datetime NOT NULL COMMENT '这个一级/二级用户这次充值的时间',
                                      `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_user_id` (`user_id`),
                                      KEY `idx_next_user_type` (`next_user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='下级及返佣关系表';

-- 返佣比例配置表（建议添加，便于后期调整比例）
CREATE TABLE if not exists `rebate_config` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                 `level_1_rebate_rate` decimal(5,2) NOT NULL DEFAULT '0.20' COMMENT '一级返佣比例',
                                 `level_2_rebate_rate` decimal(5,2) NOT NULL DEFAULT '0.07' COMMENT '二级返佣比例',
                                 `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返佣比例配置表';

-- 初始插入返佣比例配置
INSERT INTO `rebate_config` (`level_1_rebate_rate`, `level_2_rebate_rate`) VALUES (0.20, 0.07);
