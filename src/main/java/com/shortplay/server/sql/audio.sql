-- auto-generated definition
create table if not exists audio_novel
(
    id             bigint unsigned auto_increment comment '唯一主键，自增长小说ID'
    primary key,
    title          varchar(100)                                                         not null comment '小说标题，显示在列表和详情页',
    author_id      bigint unsigned                                                      not null comment '关联作者/主播ID，对应user表',
    cover_url      varchar(255)                                                         not null comment '封面图完整URL，用于详情页展示',
    thumbnail_url  varchar(255)                                                         null comment '缩略图URL，用于列表页小图展示',
    description    text                                                                 null comment '小说详细描述，支持HTML富文本',
    category_id    int unsigned                                                         not null comment '分类ID，如武侠、言情等',
    tags           varchar(255)                                                         null comment '逗号分隔的标签，用于搜索和推荐（如：悬疑,穿越,多人播讲）',
    status         enum ('draft', 'serializing', 'completed') default 'draft'           null comment '状态：draft-草稿,serializing-连载中,completed-已完结',
    is_vip         tinyint(1)                                 default 0                 null comment '是否VIP专享：0-免费,1-付费',
    price          decimal(10, 2)                             default 0.00              null comment '整本购买价格（如果是付费作品）',
    play_count     bigint                                     default 0                 null comment '总播放次数，每天定时更新',
    favorite_count bigint                                     default 0                 null comment '收藏量，实时更新',
    comment_count  bigint                                     default 0                 null comment '评论总数，异步更新',
    created_at     datetime                                   default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     datetime                                   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '最后更新时间'
    )
    comment '存储有声小说基本信息，包含作品元数据和统计信息';

create fulltext index ft_search
    on audio_novel (title, description, tags)
    comment '全文检索索引';

create index idx_author
    on audio_novel (author_id)
    comment '作者作品列表索引';

create index idx_category
    on audio_novel (category_id)
    comment '分类查询索引';



-- auto-generated definition
create table if not exists audio_chapter
(
    id           bigint unsigned auto_increment comment '章节唯一ID'
    primary key,
    novel_id     bigint unsigned                         not null comment '关联的小说ID',
    title        varchar(100)                            not null comment '章节标题（如：第1集 初入江湖）',
    chapter_no   int unsigned                            not null comment '章节编号，用于排序（1,2,3...）',
    duration     int unsigned                            not null comment '音频时长（秒），用于播放器显示',
    audio_urls   json                                    not null comment '多音质音频URL，格式：{"normal":"url","high":"url","dolby":"url"}',
    text_content longtext                                null comment '文字内容（小于500KB时直接存储）',
    text_s3_key  varchar(255)                            null comment '大文本在S3的存储路径（当text_content为空时使用）',
    is_free      tinyint(1)    default 1                 null comment '是否免费：0-付费,1-免费',
    price        decimal(6, 2) default 0.00              null comment '单章购买价格（如果是付费章节）',
    play_count   int           default 0                 null comment '本章节播放次数',
    created_at   datetime      default CURRENT_TIMESTAMP not null comment '发布时间',
    constraint uk_novel_chapter
    unique (novel_id, chapter_no) comment '防止重复章节'
    )
    comment '存储小说章节内容，包含音频文件和对应文字';

create fulltext index ft_content
    on audio_chapter (text_content)
    comment '章节内容全文索引';

create index idx_novel
    on audio_chapter (novel_id)
    comment '小说章节列表查询优化';


-- auto-generated definition
create table if not exists audio_category
(
    id         int unsigned auto_increment comment '分类唯一ID'
    primary key,
    name       varchar(20)                          not null comment '分类名称（如：武侠小说、恐怖故事）',
    icon_url   varchar(255)                         null comment '分类图标URL',
    sort_order int        default 0                 null comment '展示排序权重，越大越靠前',
    is_hot     tinyint(1) default 0                 null comment '是否热门分类：0-常规,1-热门',
    created_at datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    constraint uk_name
    unique (name) comment '分类名称唯一索引'
    )
    comment '小说分类目录，用于内容分类展示和筛选';


-- auto-generated definition
create table if not exists user_audio_favorites
(
    user_id    bigint unsigned                    not null comment '用户ID',
    novel_id   bigint unsigned                    not null comment '小说ID',
    created_at datetime default CURRENT_TIMESTAMP not null comment '收藏时间',
    primary key (user_id, novel_id) comment '复合主键防止重复收藏'
)
    comment '用户收藏关系表，实现"我的收藏"功能';

create index idx_novel
    on user_audio_favorites (novel_id)
    comment '作品被收藏统计查询';


-- auto-generated definition
create table if not exists user_audio_listening_history
(
    id             bigint unsigned auto_increment comment '记录ID'
    primary key,
    user_id        bigint unsigned                        not null comment '用户ID',
    chapter_id     bigint unsigned                        not null comment '章节ID',
    novel_id       bigint unsigned                        not null comment '冗余小说ID，避免联表查询',
    progress       int unsigned default '0'               null comment '收听进度（秒），用于续播',
    device_id      varchar(64)                            null comment '设备标识（如：iPhone12,5）',
    last_play_time datetime                               not null comment '最后播放时间',
    created_at     datetime     default CURRENT_TIMESTAMP not null comment '首次收听时间',
    constraint uk_user_chapter
    unique (user_id, chapter_id) comment '用户章节唯一记录'
    )
    comment '记录用户收听进度，实现多设备同步和续播功能';

create index idx_last_play
    on user_audio_listening_history (user_id, last_play_time)
    comment '最近收听查询优化';

create index idx_user_novel
    on user_audio_listening_history (user_id, novel_id)
    comment '用户收听历史列表查询';

