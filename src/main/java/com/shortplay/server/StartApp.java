package com.shortplay.server;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableCreateCacheAnnotation
@EnableMethodCache(basePackages = "com.shortplay.server")
@EnableScheduling
@Slf4j
public class StartApp {
    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(StartApp.class, args);
        Environment env = context.getEnvironment();
        String active = env.getProperty("spring.profiles.active");
        log.info("服务启动成功, active = {}", active);
    }

}
