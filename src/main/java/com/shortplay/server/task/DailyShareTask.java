package com.shortplay.server.task;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.UserRewardStatusPO;
import com.shortplay.server.service.UserInfoService;
import com.shortplay.server.service.UserRewardStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;

@EnableScheduling
@Service
@Slf4j
public class DailyShareTask {

    @Value("${spring.profiles.active}")
    public String active;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private UserRewardStatusService rewardStatusService;

    /**
     * 重置每日分享数据
     */
    @Scheduled(cron = "30 0 * * * ?")
    public void resetShareInfo() {
        log.info("DailyShareTask -> resetShareInfo");
        if (!"test".equals(active)) {
            log.info("skip.");
            return;
        }

        String[] timeZoneIds = TimeZone.getAvailableIDs();
        Map<Integer, String> uniqueOffsetTimeZones = new TreeMap<>();

        for (String id : timeZoneIds) {
            TimeZone timeZone = TimeZone.getTimeZone(id);
            int offset = timeZone.getRawOffset(); // 获取时区的原始UTC偏移量（不包括夏令时）

            // 如果Map中还没有这个偏移量，则添加时区ID
            int offsetInHours = offset / (3600 * 1000); // 将偏移量转换为小时
            if (!uniqueOffsetTimeZones.containsKey(offsetInHours)) {
                uniqueOffsetTimeZones.put(offsetInHours, id);
            }
        }

        // 找到当前零点的时区，重置对应用户的数据。
        for (Map.Entry<Integer, String> entry : uniqueOffsetTimeZones.entrySet()) {
            log.info("resetShareInfo, UTC Offset: " + entry.getKey() + " - Time Zone ID: " + entry.getValue());
            DateTime now = DateUtil.beginOfHour(new DateTime(TimeZone.getTimeZone(entry.getValue())));
            DateTime begin = DateUtil.beginOfDay(new DateTime(TimeZone.getTimeZone(entry.getValue())));
            log.info("now: {}, begin: {}, ? {}", now, begin, begin.equals(now));
            if (begin.equals(now)) {
                int tzOffset = entry.getKey();
                List<UserInfoPO> userList = userInfoService.lambdaQuery()
                        .eq(UserInfoPO::getTzOffset, tzOffset)
                        .list();
                for (UserInfoPO userInfo : userList) {
                    log.info("DailyShareTask -> resetShareInfo, reset, userId: {}", userInfo.getUserId());
                    rewardStatusService.lambdaUpdate()
                            .set(UserRewardStatusPO::getFacebook, false)
                            .set(UserRewardStatusPO::getWhatsapp, false)
                            .set(UserRewardStatusPO::getTwitter, false)
                            .set(UserRewardStatusPO::getInstagram, false)
                            .set(UserRewardStatusPO::getTiktok, false)
                            .eq(UserRewardStatusPO::getUserId, userInfo.getUserId())
                            .update();
                }
                break;
            }
        }
    }

}
