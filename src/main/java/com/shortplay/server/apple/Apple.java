package com.shortplay.server.apple;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.interfaces.ECPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Instant;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class Apple {

    // 配置信息从配置文件读取
    @Value("${authorization.apple.teamId}")
    private String teamId;

    @Value("${authorization.apple.clientId}")
    private String clientId;

    @Value("${authorization.apple.clientSecret}")
    private String keyId;

    @Value("${authorization.apple.url}")
    private String url;

    @Value("${authorization.apple.redirectUri}")
    private String redirectUri;

    @Value("${authorization.apple.appleJwksUrl}")
    private String appleJwksUrl;

    @Value("${authorization.apple.issuer}")
    private String issuer;

    //1.生成JWT
    public String generateClientSecret() {
        try {
            InputStream is = Apple.class.getClassLoader().getResourceAsStream("AuthKey_95WBL2M798.p8");
            String privateKeyContent = new String(is.readAllBytes(), StandardCharsets.UTF_8);
            Algorithm algorithm = Algorithm.ECDSA256(null, (ECPrivateKey) getPrivateKeyFromString(privateKeyContent));

            long now = Instant.now().getEpochSecond();
            long exp = now + 3600 * 24 * 180; // 最长有效期6个月

            return JWT.create()
                    .withKeyId(keyId)
                    .withIssuer(teamId)
                    .withIssuedAt(new Date(now * 1000))
                    .withExpiresAt(new Date(exp * 1000))
                    .withAudience("https://appleid.apple.com")
                    .withSubject(clientId)
                    .sign(algorithm);
        }catch (Exception e) {
            e.printStackTrace(); // 打印异常堆栈
            throw BizException.of(BizCode.APPLE_ERROR, "JWT生成失败"+e.getMessage());
        }
    }

    private PrivateKey getPrivateKeyFromString(String key) {
        try {
            String privateKeyPEM = key
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s+", ""); // 去除所有空白字符

            byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
            KeyFactory kf = KeyFactory.getInstance("EC");
            return kf.generatePrivate(keySpec);
        } catch (Exception e) {
            e.printStackTrace(); // 输出真实错误
            throw BizException.of(BizCode.APPLE_ERROR, "私钥解析失败"+e.getMessage());
        }
    }


    //2.验证签名
    public String exchangeToken(String code, String clientSecret){
        HttpPost post = new HttpPost(url);
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");

        List<NameValuePair> params = Arrays.asList(
                new BasicNameValuePair("grant_type", "authorization_code"),
                new BasicNameValuePair("code", code),
                new BasicNameValuePair("redirect_uri", redirectUri),
                new BasicNameValuePair("client_id", clientId),
                new BasicNameValuePair("client_secret", clientSecret)
        );

        post.setEntity(new UrlEncodedFormEntity(params));

        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(post)) {

            String responseBody = EntityUtils.toString(response.getEntity());

            JSONObject json = new JSONObject(responseBody);

            if (json.has("error")) {
                throw BizException.of(BizCode.APPLE_ERROR, "Apple返回错误"+json.getString("error"));
            }

            return json.getString("id_token");

        } catch (IOException | ParseException e) {
            e.printStackTrace(); // 可以替换成日志打印 log.error(...)
            throw BizException.of(BizCode.APPLE_ERROR, "请求Apple服务失败"+e.getMessage());
        } catch (JSONException e) {
            e.printStackTrace();
            throw BizException.of(BizCode.APPLE_ERROR, "解析Apple响应失败"+e.getMessage());
        }
    }

    //3.验证Id_token
    public boolean verify(String idToken) {
        try {
            SignedJWT signedJWT = SignedJWT.parse(idToken);

            // 1. 获取 kid 和 alg
            JWSHeader header = signedJWT.getHeader();
            String kid = header.getKeyID();
            JWSAlgorithm alg = header.getAlgorithm();

            // 2. 获取 JWKS（可自己加 Redis 缓存）
            JWKSet jwkSet = JWKSet.load(new URL(appleJwksUrl));
            JWK matchingKey = jwkSet.getKeyByKeyId(kid);
            if (matchingKey == null || !(matchingKey instanceof RSAKey)) {
                log.error("找不到匹配 kid 的公钥：{}", kid);
                throw BizException.of(BizCode.APPLE_ERROR, "找不到匹配 kid 的公钥"+kid);
            }

            // 3. 验证签名
            RSAKey rsaKey = (RSAKey) matchingKey;
            JWSVerifier verifier = new RSASSAVerifier(rsaKey);
            if (!signedJWT.verify(verifier)) {
                log.error("");
                throw BizException.of(BizCode.APPLE_ERROR, "JWT 签名无效");
            }

            // 4. 校验 Claims
            JWTClaimsSet claims = signedJWT.getJWTClaimsSet();

            if (!issuer.equals(claims.getIssuer())) {
                log.error("JWT issuer 不合法: {}", claims.getIssuer());
                throw BizException.of(BizCode.APPLE_ERROR, "JWT issuer 不合法"+claims.getIssuer());
            }

            if (!clientId.equals(claims.getAudience().get(0))) {
                log.error("JWT audience 不合法: {}", claims.getAudience());
                throw BizException.of(BizCode.APPLE_ERROR, "JWT audience 不合法"+claims.getAudience());
            }

            Date now = new Date();
            if (claims.getExpirationTime().before(now)) {
                log.error("JWT 已过期");
                throw BizException.of(BizCode.APPLE_ERROR, "JWT 已过期");
            }

            // ✅ 验证通过，打印用户信息
//            log.info("sub: {}", claims.getSubject());
//            log.info("email: {}", claims.getStringClaim("email"));
//            log.info("email_verified: {}", claims.getBooleanClaim("email_verified"));
            return true;

        } catch (Exception e) {
            log.error("验证 id_token 异常", e);
            throw BizException.of(BizCode.APPLE_ERROR, "验证 id_token 异常"+e.getMessage());
        }
    }

    //4.解析验证结果
    public DecodedJWT parse(String idToken) {
        return JWT.decode(idToken);
    }

    public String printUserInfo(String idToken) {
        DecodedJWT jwt = parse(idToken);
//        System.out.println("sub: " + jwt.getSubject()); // Apple 用户唯一 ID
//        System.out.println("email: " + jwt.getClaim("email").asString());
//        System.out.println("email_verified: " + jwt.getClaim("email_verified").asBoolean());
        return jwt.getSubject();
    }

}
