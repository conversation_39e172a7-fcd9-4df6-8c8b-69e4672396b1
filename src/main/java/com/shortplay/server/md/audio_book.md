# 有声书

## 1.实现有声书展示、详情页和播放功能

### 1.1 获取有声书列表

GET http://localhost:18080/api/audio/novel/list?sort=recommend&page=1&pageSize=10

**Header:**

| 字段名   | 字段类型 | 是否必填 | 字段解释               |
| -------- | -------- | -------- | ---------------------- |
| language | String   | 是       | 语言 ar 阿拉伯 en 英文 |

**Path Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释                                                     |
| -------- | -------- | -------- | ------------------------------------------------------------ |
| sort     | String   | 否       | 排序方式 recommend：推荐（默认） hot：按热度排序 completed：已完结 |
| page     | Integer  | 否       | 页码，默认 1                                                 |
| pageSize | Integer  | 否       | 每页条数，默认 10                                            |

**Returns:**

| 字段名 | 字段类型      | 是否必返回 | 字段解释       |
| ------ | ------------- | ---------- | -------------- |
| ts     | Long          | 是         | 时间戳         |
| code   | Long          | 是         | 错误码         |
| msg    | String        | 是         | 错误信息       |
| data   | NovelListResp | 是         | 有声书列表信息 |

**NovelListResp:**

| 字段名   | 字段类型            | 是否必返回 | 字段解释       |
| -------- | ------------------- | ---------- | -------------- |
| total    | Long                | 是         | 总记录数       |
| page     | Integer             | 是         | 当前页码       |
| pageSize | Integer             | 是         | 每页条数       |
| list     | List<NovelInfoResp> | 是         | 有声书信息列表 |

**NovelInfoResp:**

| 字段名        | 字段类型 | 是否必填 | 字段解释                            |
| ------------- | -------- | -------- | ----------------------------------- |
| id            | Long     | 是       | 有声书唯一标识                      |
| title         | String   | 是       | 有声书标题                          |
| authorId      | Long     | 是       | 作者/主播 ID                        |
| coverUrl      | String   | 是       | 封面图 URL                          |
| thumbnailUrl  | String   | 否       | 缩略图 URL                          |
| playCount     | Long     | 是       | 总播放次数                          |
| favoriteCount | Long     | 是       | 收藏数量                            |
| commentCount  | Long     | 是       | 评论总数                            |
| status        | String   | 是       | 状态（draft/serializing/completed） |
| isVip         | Boolean  | 是       | 是否 VIP 专享                       |
| createdAt     | Long     | 是       | 创建时间（毫秒）                    |
| updatedAt     | Long     | 是       | 最后更新时间（毫秒）                |

```json
{
  "ts": 1721226325000,
  "code": 200,
  "msg": "success",
  "data": {
    "total": 150,
    "page": 1,
    "pageSize": 10,
    "list": [
      {
        "id": 10001,
        "title": "武侠江湖录",
        "authorId": 2001,
        "coverUrl": "https://example.com/covers/jianghu.jpg",
        "thumbnailUrl": "https://example.com/thumbnails/jianghu_thumb.jpg",
        "playCount": 85000,
        "favoriteCount": 3200,
        "commentCount": 450,
        "status": "serializing",
        "isVip": false,
        "createdAt": 1721126325000,
        "updatedAt": 1721226325000
      },
      {
        "id": 10002,
        "title": "穿越时空的恋人",
        "authorId": 2002,
        "coverUrl": "https://example.com/covers/time_love.jpg",
        "thumbnailUrl": "https://example.com/thumbnails/time_love_thumb.jpg",
        "playCount": 62000,
        "favoriteCount": 1800,
        "commentCount": 300,
        "status": "completed",
        "isVip": true,
        "createdAt": 1721026325000,
        "updatedAt": 1721226325000
      }
    ]
  }
}
```

### 1.2 获取有声书详情

GET http://localhost:18080/api/audio/novel/detail?novelId=10001

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释               |
| ------------- | -------- | -------- | ---------------------- |
| language      | String   | 是       | 语言 ar 阿拉伯 en 英文 |
| authorization | String   | 否       | JWT Token              |

**Path Parameters:**

| 字段名  | 字段类型 | 是否必填 | 字段解释       |
| ------- | -------- | -------- | -------------- |
| novelId | Long     | 是       | 有声书唯一标识 |

**Returns:**

| 字段名 | 字段类型        | 是否必返回 | 字段解释 |
| ------ | --------------- | ---------- | -------- |
| ts     | Long            | 是         | 时间戳   |
| code   | Long            | 是         | 错误码   |
| msg    | String          | 是         | 错误信息 |
| data   | NovelDetailResp | 是         | 详情信息 |

**NovelDetailResp:**

| 字段名        | 字段类型     | 是否必填 | 字段解释                            |
| ------------- | ------------ | -------- | ----------------------------------- |
| id            | Long         | 是       | 有声书唯一标识                      |
| title         | String       | 是       | 有声书标题                          |
| authorId      | Long         | 是       | 作者/主播 ID                        |
| coverUrl      | String       | 是       | 封面图 URL                          |
| description   | String       | 否       | 详细描述                            |
| categoryId    | Integer      | 是       | 分类 ID                             |
| tags          | List<String> | 否       | 标签列表                            |
| status        | String       | 是       | 状态（draft/serializing/completed） |
| isVip         | Boolean      | 是       | 是否 VIP 专享                       |
| price         | Decimal      | 否       | 整本购买价格                        |
| playCount     | Long         | 是       | 总播放次数                          |
| favoriteCount | Long         | 是       | 收藏数量                            |
| commentCount  | Long         | 是       | 评论总数                            |
| createdAt     | Long         | 是       | 创建时间（毫秒）                    |
| updatedAt     | Long         | 是       | 最后更新时间（毫秒）                |
| isCollected   | Boolean      | 否       | 当前用户是否收藏（需登录）          |
| authorAvatar  | String       | 是       | 作者头像 URL                        |

```json
{
  "ts": 1721226326000,
  "code": 200,
  "msg": "success",
  "data": {
    "id": 10001,
    "title": "武侠江湖录",
    "authorId": 2001,
    "coverUrl": "https://example.com/covers/jianghu.jpg",
    "description": "<p>经典武侠故事，精彩纷呈...</p>",
    "categoryId": 1,
    "tags": ["武侠", "冒险", "多人播讲"],
    "status": "serializing",
    "isVip": false,
    "price": 0.00,
    "playCount": 85000,
    "favoriteCount": 3200,
    "commentCount": 450,
    "createdAt": 1721126325000,
    "updatedAt": 1721226325000,
    "isCollected": false,
    "authorAvatar": "https://example.com/avatars/author2001.jpg"
  }
}
```

### 1.3 获取章节列表

GET http://localhost:18080/api/audio/chapter/list?novelId=10001&page=1&pageSize=20

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释               |
| ------------- | -------- | -------- | ---------------------- |
| language      | String   | 是       | 语言 ar 阿拉伯 en 英文 |
| authorization | String   | 否       | JWT Token              |

**Path Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释          |
| -------- | -------- | -------- | ----------------- |
| novelId  | Long     | 是       | 有声书唯一标识    |
| page     | Integer  | 否       | 页码，默认 1      |
| pageSize | Integer  | 否       | 每页条数，默认 20 |

**Returns**:

| 字段名 | 字段类型        | 是否必返回 | 字段解释     |
| ------ | --------------- | ---------- | ------------ |
| ts     | Long            | 是         | 时间戳       |
| code   | Long            | 是         | 错误码       |
| msg    | String          | 是         | 错误信息     |
| data   | ChapterListResp | 是         | 章节列表信息 |

**ChapterListResp:**

| 字段名   | 字段类型              | 是否必返回 | 字段解释     |
| -------- | --------------------- | ---------- | ------------ |
| total    | Long                  | 是         | 总章节数     |
| page     | Integer               | 是         | 当前页码     |
| pageSize | Integer               | 是         | 每页条数     |
| list     | List<ChapterInfoResp> | 是         | 章节信息列表 |

**ChapterInfoResp:**

| 字段名      | 字段类型 | 是否必填 | 字段解释                                       |
| ----------- | -------- | -------- | ---------------------------------------------- |
| id          | Long     | 是       | 章节唯一标识                                   |
| title       | String   | 是       | 章节标题                                       |
| chapterNo   | Integer  | 是       | 章节编号                                       |
| duration    | Integer  | 是       | 音频时长（秒）                                 |
| audioUrls   | JSON     | 是       | 多音质音频 URL，支持 normal, high, dolby       |
| isFree      | Boolean  | 是       | 是否免费                                       |
| price       | Decimal  | 否       | 单章购买价格                                   |
| playCount   | Integer  | 是       | 章节播放次数                                   |
| createdAt   | Long     | 是       | 创建时间（毫秒）                               |
| isPurchased | Boolean  | 否       | 当前用户是否已购买（需登录，未登录返回 false） |

```json
{
  "ts": 1721226327000,
  "code": 200,
  "msg": "success",
  "data": {
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "list": [
      {
        "id": 20001,
        "title": "第1集 初入江湖",
        "chapterNo": 1,
        "duration": 1800,
        "audioUrls": {
          "normal": "https://example.com/audio/10001/1_normal.mp3",
          "high": "https://example.com/audio/10001/1_high.mp3"
        },
        "isFree": true,
        "price": 0.00,
        "playCount": 12000,
        "createdAt": 1721126326000
      },
      {
        "id": 20002,
        "title": "第2集 剑挑群雄",
        "chapterNo": 2,
        "duration": 2000,
        "audioUrls": {
          "normal": "https://example.com/audio/10001/2_normal.mp3",
          "high": "https://example.com/audio/10001/2_high.mp3"
        },
        "isFree": false,
        "price": 2.99,
        "playCount": 9500,
        "createdAt": 1721126327000
      }
    ]
  }
}
```

## 2.有声书上传和存储功能

### 2.1 上传有声书

POST http://localhost:18080/api/audio/novel/upload

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释            |
| ------------- | -------- | -------- | ------------------- |
| authorization | String   | 是       | 用户 JWT Token      |
| Content-Type  | String   | 是       | multipart/form-data |

**Body Parameters:**

| 字段名      | 字段类型     | 是否必填 | 字段解释                            |
| ----------- | ------------ | -------- | ----------------------------------- |
| title       | String       | 是       | 有声书标题                          |
| authorId    | Long         | 是       | 作者/主播 ID                        |
| coverFile   | File         | 是       | 封面图片文件                        |
| description | String       | 否       | 详细描述                            |
| categoryId  | Integer      | 是       | 分类 ID                             |
| tags        | List<String> | 否       | 标签列表                            |
| status      | String       | 否       | 状态（draft/serializing/completed） |
| isVip       | Boolean      | 否       | 是否 VIP 专享                       |
| price       | Decimal      | 否       | 整本购买价格                        |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释      |
| ------ | -------- | ---------- | ------------- |
| ts     | Long     | 是         | 时间戳        |
| code   | Long     | 是         | 错误码        |
| msg    | String   | 是         | 错误信息      |
| data   | Long     | 是         | 新增有声书 ID |

```json
{
  "ts": 1721226328000,
  "code": 200,
  "msg": "上传成功",
  "data": 10003
}
```

## 3.点赞、收藏、评论、分享功能

### 3.1 点赞/取消点赞有声书

#### 3.1.1 点赞有声书

POST http://localhost:18080/api/audio/novel/like

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Parameters:**

| 字段名  | 字段类型 | 是否必填 | 字段解释       |
| ------- | -------- | -------- | -------------- |
| novelId | Long     | 是       | 有声书唯一标识 |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释 |
| ------ | -------- | ---------- | -------- |
| ts     | Long     | 是         | 时间戳   |
| code   | Long     | 是         | 错误码   |
| msg    | String   | 是         | 错误信息 |
| data   | Long     | 是         | 点赞数量 |

```json
{
  "ts": 1721226329000,
  "code": 200,
  "msg": "OK",
  "data": 3200
}
```

#### 3.1.2 取消点赞有声书

POST http://localhost:18080/api/audio/novel/likeCancel

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Parameters:**

| 字段名  | 字段类型 | 是否必填 | 字段解释       |
| ------- | -------- | -------- | -------------- |
| novelId | Long     | 是       | 有声书唯一标识 |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释 |
| ------ | -------- | ---------- | -------- |
| ts     | Long     | 是         | 时间戳   |
| code   | Long     | 是         | 错误码   |
| msg    | String   | 是         | 错误信息 |
| data   | Long     | 是         | 点赞数量 |

```json
{
  "ts": 1721226329000,
  "code": 200,
  "msg": "OK",
  "data": 3200
}
```

### 3.2 收藏/取消收藏有声书

#### 3.2.1 收藏有声书

POST http://localhost:18080/api/audio/novel/collect

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Parameters:**

| 字段名  | 字段类型 | 是否必填 | 字段解释       |
| ------- | -------- | -------- | -------------- |
| novelId | Long     | 是       | 有声书唯一标识 |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释 |
| ------ | -------- | ---------- | -------- |
| ts     | Long     | 是         | 时间戳   |
| code   | Long     | 是         | 错误码   |
| msg    | String   | 是         | 错误信息 |
| data   | Long     | 是         | 收藏数量 |

```json
{
  "ts": 1721226330000,
  "code": 200,
  "msg": "OK",
  "data": 3200
}
```

#### 3.2.2 取消收藏有声书

POST http://localhost:18080/api/audio/novel/collectCancel

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Parameters:**

| 字段名  | 字段类型 | 是否必填 | 字段解释       |
| ------- | -------- | -------- | -------------- |
| novelId | Long     | 是       | 有声书唯一标识 |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释 |
| ------ | -------- | ---------- | -------- |
| ts     | Long     | 是         | 时间戳   |
| code   | Long     | 是         | 错误码   |
| msg    | String   | 是         | 错误信息 |
| data   | Long     | 是         | 收藏数量 |

```json
{
  "ts": 1721226330000,
  "code": 200,
  "msg": "OK",
  "data": 3200
}
```

### 3.3 发表有声书评论

POST http://localhost:18080/api/audio/novel/comment/send

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释       |
| ------------- | -------- | -------- | -------------- |
| authorization | String   | 是       | 用户 JWT Token |

**Body Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释                                                    |
| -------- | -------- | -------- | ----------------------------------------------------------- |
| novelId  | Long     | 是       | 有声书唯一标识                                              |
| content  | String   | 是       | 评论内容（1-500 字，不可为空）                              |
| parentId | Long     | 否       | 上级评论 ID（用于回复评论），无上级时无需传该字段（默认 0） |

**Returns:**

| 字段名 | 字段类型    | 是否必返回 | 字段解释           |
| ------ | ----------- | ---------- | ------------------ |
| ts     | Long        | 是         | 时间戳             |
| code   | Long        | 是         | 错误码             |
| msg    | String      | 是         | 错误信息           |
| data   | CommentResp | 是         | 新增有声书评论信息 |

**CommentResp:**

| 字段名          | 字段类型 | 是否必填 | 字段解释                             |
| --------------- | -------- | -------- | ------------------------------------ |
| commentId       | Long     | 是       | 评论唯一标识                         |
| novelId         | Long     | 是       | 关联的有声书 ID                      |
| content         | String   | 是       | 评论内容                             |
| parentId        | Long     | 是       | 上级评论 ID（根评论为 0）            |
| rootId          | Long     | 是       | 根评论 ID（子评论关联的根评论）      |
| createTime      | Long     | 是       | 创建时间戳（毫秒）                   |
| likeCount       | Long     | 是       | 点赞数量                             |
| isLiked         | Boolean  | 否       | 当前用户是否已点赞（用户登录时返回） |
| replyCount      | Long     | 否       | 子评论数量（仅根评论有）             |
| userInfo        | Object   | 是       | 评论用户信息（头像、昵称等）         |
| replyToUserInfo | Object   | 否       | 被回复用户信息（仅子评论有）         |

```json
{
  "ts": 1721226350000,
  "code": 200,
  "msg": "评论成功",
  "data": {
    "commentId": 30001,
    "novelId": 10001,
    "content": "这部有声书太精彩了！",
    "parentId": 0,
    "rootId": 0,
    "createTime": 1721226350000,
    "likeCount": 0,
    "isLiked": false,
    "userInfo": {
      "userId": 2001,
      "userName": "听书爱好者",
      "avatarUrl": "https://example.com/avatars/2001.jpg"
    }
  }
}
```

### 3.4 获取有声书根评论列表

GET http://localhost:18080/api/audio/novel/comment/getRootComment?novelId=10001&page=1&pageSize=20

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释                                |
| ------------- | -------- | -------- | --------------------------------------- |
| language      | String   | 是       | 语言代码，ar = 阿拉伯语，en = 英语      |
| authorization | String   | 否       | 用户 JWT Token（用于获取 isLiked 状态） |

**Path Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释          |
| -------- | -------- | -------- | ----------------- |
| novelId  | Long     | 是       | 有声书唯一标识    |
| page     | Integer  | 否       | 页码，默认 1      |
| pageSize | Integer  | 否       | 每页条数，默认 20 |

**Returns:**

| 字段名 | 字段类型        | 是否必返回 | 字段解释             |
| ------ | --------------- | ---------- | -------------------- |
| ts     | Long            | 是         | 时间戳               |
| code   | Long            | 是         | 错误码               |
| msg    | String          | 是         | 错误信息             |
| data   | CommentListResp | 是         | 根评论列表及分页信息 |

**CommentListResp:**

| 字段名   | 字段类型          | 是否必返回 | 字段解释   |
| -------- | ----------------- | ---------- | ---------- |
| total    | Long              | 是         | 总评论数   |
| page     | Integer           | 是         | 当前页码   |
| pageSize | Integer           | 是         | 每页条数   |
| list     | List<CommentResp> | 是         | 根评论列表 |

**CommentResp:**

| 字段名          | 字段类型 | 是否必填 | 字段解释                             |
| --------------- | -------- | -------- | ------------------------------------ |
| commentId       | Long     | 是       | 评论唯一标识                         |
| novelId         | Long     | 是       | 关联的有声书 ID                      |
| content         | String   | 是       | 评论内容                             |
| parentId        | Long     | 是       | 上级评论 ID（根评论为 0）            |
| rootId          | Long     | 是       | 根评论 ID（子评论关联的根评论）      |
| createTime      | Long     | 是       | 创建时间戳（毫秒）                   |
| likeCount       | Long     | 是       | 点赞数量                             |
| isLiked         | Boolean  | 否       | 当前用户是否已点赞（用户登录时返回） |
| replyCount      | Long     | 否       | 子评论数量（仅根评论有）             |
| userInfo        | Object   | 是       | 评论用户信息（头像、昵称等）         |
| replyToUserInfo | Object   | 否       | 被回复用户信息（仅子评论有）         |

```json
{
  "ts": 1721226351000,
  "code": 200,
  "msg": "success",
  "data": {
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "list": [
      {
        "commentId": 30001,
        "novelId": 10001,
        "content": "这部有声书太精彩了！",
        "parentId": 0,
        "rootId": 0,
        "createTime": 1721226350000,
        "likeCount": 10,
        "isLiked": true,
        "replyCount": 3,
        "userInfo": {
          "userId": 2001,
          "userName": "听书爱好者",
          "avatarUrl": "https://example.com/avatars/2001.jpg"
        }
      }
    ]
  }
}
```

### 3.5 获取有声书子评论列表

GET http://localhost:18080/api/audio/novel/comment/getChildComment?rootId=30001&page=1&pageSize=20

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释                                |
| ------------- | -------- | -------- | --------------------------------------- |
| language      | String   | 是       | 语言代码，ar = 阿拉伯语，en = 英语      |
| authorization | String   | 否       | 用户 JWT Token（用于获取 isLiked 状态） |

**Path Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释                                |
| -------- | -------- | -------- | --------------------------------------- |
| rootId   | Long     | 是       | 根评论唯一 ID（对应根评论的 commentId） |
| page     | Integer  | 否       | 页码，默认 1                            |
| pageSize | Integer  | 否       | 每页条数，默认 20                       |

**Returns:**

| 字段名 | 字段类型        | 是否必返回 | 字段解释             |
| ------ | --------------- | ---------- | -------------------- |
| ts     | Long            | 是         | 时间戳               |
| code   | Long            | 是         | 错误码               |
| msg    | String          | 是         | 错误信息             |
| data   | CommentListResp | 是         | 子评论列表及分页信息 |

**CommentListResp:**

| 字段名   | 字段类型          | 是否必返回 | 字段解释   |
| -------- | ----------------- | ---------- | ---------- |
| total    | Long              | 是         | 总评论数   |
| page     | Integer           | 是         | 当前页码   |
| pageSize | Integer           | 是         | 每页条数   |
| list     | List<CommentResp> | 是         | 根评论列表 |

**CommentResp:**

| 字段名          | 字段类型 | 是否必填 | 字段解释                             |
| --------------- | -------- | -------- | ------------------------------------ |
| commentId       | Long     | 是       | 评论唯一标识                         |
| novelId         | Long     | 是       | 关联的有声书 ID                      |
| content         | String   | 是       | 评论内容                             |
| parentId        | Long     | 是       | 上级评论 ID（根评论为 0）            |
| rootId          | Long     | 是       | 根评论 ID（子评论关联的根评论）      |
| createTime      | Long     | 是       | 创建时间戳（毫秒）                   |
| likeCount       | Long     | 是       | 点赞数量                             |
| isLiked         | Boolean  | 否       | 当前用户是否已点赞（用户登录时返回） |
| replyCount      | Long     | 否       | 子评论数量（仅根评论有）             |
| userInfo        | Object   | 是       | 评论用户信息（头像、昵称等）         |
| replyToUserInfo | Object   | 否       | 被回复用户信息（仅子评论有）         |

```json
{
  "ts": 1721226352000,
  "code": 200,
  "msg": "success",
  "data": {
    "total": 3,
    "page": 1,
    "pageSize": 20,
    "list": [
      {
        "commentId": 30002,
        "novelId": 10001,
        "content": "同意楼主的观点！",
        "parentId": 30001,
        "rootId": 30001,
        "createTime": 1721226351000,
        "likeCount": 5,
        "isLiked": false,
        "userInfo": {
          "userId": 2002,
          "userName": "听书新手",
          "avatarUrl": "https://example.com/avatars/2002.jpg"
        },
        "replyToUserInfo": {
          "userId": 2001,
          "userName": "听书爱好者"
        }
      }
    ]
  }
}
```

### 3.6 点赞 / 取消点赞有声书评论

POST http://localhost:18080/api/audio/novel/comment/like

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释       |
| ------------- | -------- | -------- | -------------- |
| authorization | String   | 是       | 用户 JWT Token |

**Body Parameters:**

| 字段名    | 字段类型 | 是否必填 | 字段解释                      |
| --------- | -------- | -------- | ----------------------------- |
| commentId | Long     | 是       | 评论唯一标识                  |
| isLike    | Boolean  | 是       | true = 点赞，false = 取消点赞 |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释     |
| ------ | -------- | ---------- | ------------ |
| ts     | Long     | 是         | 时间戳       |
| code   | Long     | 是         | 错误码       |
| msg    | String   | 是         | 错误信息     |
| data   | Object   | 是         | 点赞结果信息 |

```json
{
  "ts": 1721226353000,
  "code": 200,
  "msg": "点赞成功",
  "data": {
    "commentId": 30001,
    "likeCount": 11,
    "isLiked": true
  }
}
```

### 3.7 分享有声书

POST http://localhost:18080/api/audio/novel/share

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Body Parameters:**

| 字段名  | 字段类型 | 是否必填 | 字段解释       |
| ------- | -------- | -------- | -------------- |
| novelId | Long     | 是       | 有声书唯一标识 |
| channel | String   | 是       | 分享渠道       |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释 |
| ------ | -------- | ---------- | -------- |
| ts     | Long     | 是         | 时间戳   |
| code   | Long     | 是         | 错误码   |
| msg    | String   | 是         | 错误信息 |
| data   | Object   | 是         | 分享结果 |

**Data:**

| 字段名     | 字段类型 | 是否必填 | 字段解释   |
| ---------- | -------- | -------- | ---------- |
| shareUrl   | String   | 是       | 分享链接   |
| shareCount | Long     | 是       | 总分享次数 |
| channel    | String   | 是       | 分享渠道   |

```json
{
  "ts": 1721226332000,
  "code": 200,
  "msg": "分享成功",
  "data": {
    "shareUrl": "https://example.com/share/audio?aid=10001&uid=2001",
    "shareCount": 120,
    "channel": "facebook"
  }
}
```

## 4.记录用户播放历史和续播

### 4.1 记录/更新播放进度

POST http://localhost:18080/api/audio/history/update

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Body Parameters:**

| 字段名    | 字段类型 | 是否必填 | 字段解释       |
| --------- | -------- | -------- | -------------- |
| chapterId | Long     | 是       | 章节唯一标识   |
| progress  | Integer  | 是       | 收听进度（秒） |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释     |
| ------ | -------- | ---------- | ------------ |
| ts     | Long     | 是         | 时间戳       |
| code   | Long     | 是         | 错误码       |
| msg    | String   | 是         | 错误信息     |
| data   | Boolean  | 是         | 更新成功与否 |

```json
{
  "ts": 1721226333000,
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 4.2 获取播放历史

GET http://localhost:18080/api/audio/history/list?page=1&pageSize=10

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Path Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释          |
| -------- | -------- | -------- | ----------------- |
| page     | Integer  | 否       | 页码，默认 1      |
| pageSize | Integer  | 否       | 每页条数，默认 10 |

**Returns:**

| 字段名 | 字段类型             | 是否必返回 | 字段解释     |
| ------ | -------------------- | ---------- | ------------ |
| ts     | Long                 | 是         | 时间戳       |
| code   | Long                 | 是         | 错误码       |
| msg    | String               | 是         | 错误信息     |
| data   | NovelHistoryListResp | 是         | 历史记录列表 |

**NovelHistoryListResp:**

| 字段名   | 字段类型               | 是否必返回 | 字段解释     |
| -------- | ---------------------- | ---------- | ------------ |
| total    | Long                   | 是         | 总记录数     |
| page     | Integer                | 是         | 当前页码     |
| pageSize | Integer                | 是         | 每页条数     |
| list     | List<NovelHistoryResp> | 是         | 历史记录列表 |

**NovelHistoryResp:**

| 字段名       | 字段类型 | 是否必填 | 字段解释             |
| ------------ | -------- | -------- | -------------------- |
| chapterId    | Long     | 是       | 章节唯一标识         |
| novelId      | Long     | 是       | 有声书 ID            |
| title        | String   | 是       | 章节标题             |
| progress     | Integer  | 是       | 收听进度（秒）       |
| lastPlayTime | Long     | 是       | 最后播放时间（毫秒） |
| createdAt    | Long     | 是       | 首次收听时间（毫秒） |

```json
{
  "ts": 1721226334000,
  "code": 200,
  "msg": "success",
  "data": {
    "total": 25,
    "page": 1,
    "pageSize": 10,
    "list": [
      {
        "chapterId": 20001,
        "novelId": 10001,
        "title": "第1集 初入江湖",
        "progress": 1200,
        "lastPlayTime": 1721226333000,
        "createdAt": 1721126326000
      }
    ]
  }
}
```

## 5.用户创建和播放书单

### 5.1 创建书单

POST http://localhost:18080/api/audio/playlist/create

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Body Parameters:**

| 字段名   | 字段类型   | 是否必填 | 字段解释               |
| -------- | ---------- | -------- | ---------------------- |
| name     | String     | 是       | 书单名称               |
| novelIds | List<Long> | 是       | 包含的有声书 ID 列表   |
| isPublic | Boolean    | 否       | 是否公开（默认 false） |

**Returns:**

| 字段名 | 字段类型 | 是否必返回 | 字段解释 |
| ------ | -------- | ---------- | -------- |
| ts     | Long     | 是         | 时间戳   |
| code   | Long     | 是         | 错误码   |
| msg    | String   | 是         | 错误信息 |
| data   | Long     | 是         | 书单 ID  |

```json
{
  "ts": 1721226335000,
  "code": 200,
  "msg": "创建成功",
  "data": 30001
}
```

### 5.2 获取用户书单

GET http://localhost:18080/api/audio/playlist/list?page=1&pageSize=10

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Path Parameters:**

| 字段名   | 字段类型 | 是否必填 | 字段解释          |
| -------- | -------- | -------- | ----------------- |
| page     | Integer  | 否       | 页码，默认 1      |
| pageSize | Integer  | 否       | 每页条数，默认 10 |

**Returns:**

| 字段名 | 字段类型         | 是否必返回 | 字段解释     |
| ------ | ---------------- | ---------- | ------------ |
| ts     | Long             | 是         | 时间戳       |
| code   | Long             | 是         | 错误码       |
| msg    | String           | 是         | 错误信息     |
| data   | PlaylistListResp | 是         | 书单列表信息 |

**PlaylistListResp:**

| 字段名   | 字段类型           | 是否必返回 | 字段解释     |
| -------- | ------------------ | ---------- | ------------ |
| total    | Long               | 是         | 总记录数     |
| page     | Integer            | 是         | 当前页码     |
| pageSize | Integer            | 是         | 每页条数     |
| list     | List<PlaylistResp> | 是         | 书单信息列表 |

**PlaylistResp:**

| 字段名    | 字段类型   | 是否必填 | 字段解释             |
| --------- | ---------- | -------- | -------------------- |
| id        | Long       | 是       | 书单唯一标识         |
| name      | String     | 是       | 书单名称             |
| novelIds  | List<Long> | 是       | 包含的有声书 ID 列表 |
| createdAt | Long       | 是       | 创建时间（毫秒）     |

```json
{
  "ts": 1721226336000,
  "code": 200,
  "msg": "success",
  "data": {
    "total": 5,
    "page": 1,
    "pageSize": 10,
    "list": [
      {
        "id": 30001,
        "name": "我的武侠书单",
        "novelIds": [10001, 10003],
        "createdAt": 1721226335000
      }
    ]
  }
}
```

### 5.3 播放书单

POST http://localhost:18080/api/audio/playlist/play

**Header:**

| 字段名        | 字段类型 | 是否必填 | 字段解释  |
| ------------- | -------- | -------- | --------- |
| authorization | String   | 是       | JWT Token |

**Body Parameters:**

| 字段名     | 字段类型 | 是否必填 | 字段解释     |
| ---------- | -------- | -------- | ------------ |
| playlistId | Long     | 是       | 书单唯一标识 |

**Returns:**

| 字段名 | 字段类型         | 是否必返回 | 字段解释 |
| ------ | ---------------- | ---------- | -------- |
| ts     | Long             | 是         | 时间戳   |
| code   | Long             | 是         | 错误码   |
| msg    | String           | 是         | 错误信息 |
| data   | PlaylistPlayResp | 是         | 播放信息 |

**PlaylistPlayResp:**

| 字段名    | 字段类型 | 是否必填 | 字段解释            |
| --------- | -------- | -------- | ------------------- |
| novelId   | Long     | 是       | 当前播放的有声书 ID |
| chapterId | Long     | 是       | 当前播放的章节 ID   |
| audioUrl  | String   | 是       | 音频 URL            |

```json
{
  "ts": 1721226337000,
  "code": 200,
  "msg": "播放开始",
  "data": {
    "novelId": 10001,
    "chapterId": 20001,
    "audioUrl": "https://example.com/audio/10001/1_normal.mp3"
  }
}
```

