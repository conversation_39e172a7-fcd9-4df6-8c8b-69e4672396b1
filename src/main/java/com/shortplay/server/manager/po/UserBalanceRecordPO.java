package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_balance_record")
public class UserBalanceRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户 ID
     */
    @TableField("userId")
    private String userId;

    /**
     * 奖励变化量
     */
    @TableField("bonus")
    private Long bonus;

    /**
     * 金币变化量
     */
    @TableField("coins")
    private Long coins;

    /**
     * 操作后奖励余额
     */
    @TableField("afterBonus")
    private Long afterBonus;

    /**
     * 操作后金币余额
     */
    @TableField("afterCoins")
    private Long afterCoins;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;
}
