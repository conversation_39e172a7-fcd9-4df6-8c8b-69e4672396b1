package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/12/17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_goods_order")
public class GoodsOrderPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品订单号,唯一键
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 支付订单号
     */
    @TableField(value = "paypal_id")
    private String paypalId;

    /**
     * 商品详情
     */
    @TableField(value = "goods_detail")
    private String goodsDetail;

    /**
     * 支付金额，USD计价
     */
    @TableField(value = "money")
    private Double money;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 支付方式 credit_card, paypal
     */
    @TableField(value = "pay_type")
    private String payType;

    /**
     * 支付时间 init 初始态 finish 已完成
     */
    @TableField("pay_status")
    private String payStatus;

    /**
     * 完成时间
     */
    @TableField("pay_done_time")
    private Long payDoneTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;

}
