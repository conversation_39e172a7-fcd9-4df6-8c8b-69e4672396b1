package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/1/9
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_chapter_relation")
public class UserChapterRelationPO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 联合唯一键,book_id在中
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 联合唯一键，uid在前
     */
    @TableField("user_id")
    private String userId;

    /**
     * 联合唯一键，chapterId在后
     */
    @TableField("chapter_id")
    private Long chapterId;

    /**
     * 是否喜爱
     */
    @TableField("match_like")
    private boolean matchLike;

    /**
     * 是否收藏
     */
    @TableField("match_collect")
    private boolean matchCollect;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;

}
