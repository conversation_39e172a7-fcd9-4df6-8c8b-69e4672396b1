package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2023/12/17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_unlock_history")
public class UnlockHistoryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id，普通索引
     */
    @TableField("user_id")
    private String userId;

    /**
     * 系列id
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 系列名称
     */
    @TableField("title")
    private String title;

    /**
     * 章节id
     */
    @TableField("chapter_id")
    private Long chapterId;

    /**
     * 花费数量
     */
    @TableField("cost_num")
    private Long costNum;

    /**
     * 交易时间
     */
    @TableField("transaction_time")
    private Long transactionTime;

}
