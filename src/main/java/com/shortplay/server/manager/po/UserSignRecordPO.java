package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_sign_record")
public class UserSignRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户 ID
     */
    @TableField("userId")
    private String userId;

    /**
     * 签到日期
     * yy-mm-dd
     */
    @TableField("dateStr")
    private String dateStr;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;
}
