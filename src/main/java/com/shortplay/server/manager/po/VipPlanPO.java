package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/7/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("vip_plan")
public class VipPlanPO implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 计划名称（英文）
     */
    @TableField("name")
    private String name;

    @TableField("name_ar")
    private String nameAr;

    /**
     * 价格（美元）
     */
    @TableField("price")
    private Double price;

    /**
     * 持续天数
     */
    @TableField("duration_days")
    private Integer durationDays;

    /**
     * 权益描述（英文）
     */
    @TableField("description")
    private String description;

    @TableField("description_ar")
    private String descriptionAr;

    @TableField("created_at")
    private Long createAt;

    @TableField("updated_at")
    private Long updatedAt;
}
