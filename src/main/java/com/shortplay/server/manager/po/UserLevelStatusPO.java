package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@TableName("tbl_user_level_status")
public class UserLevelStatusPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 总充值金额
     */
    @TableField(value = "total_amount")
    private double totalAmount;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private long updateTime;

    /**
     * 消费等级
     */
    @TableField(value = "cost_level")
    private int costLevel;
//
//    /**
//     * 消费等级描述
//     */
//    @TableField(value = "cost_level_disc")
//    private String costLevelDesc;
}















