package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_watch_time")
public class UserWatchTimePO {
  @TableId(value = "id", type = IdType.AUTO)
  @JsonIgnore
  private Long id;

  /**
   * 联合唯一键
   */
  @TableField("chapter_id")
  private Long chapterId;

  /**
   * 联合唯一键
   */
  @TableField("book_id")
  private Long bookId;


  @TableField("uuid")
  private Integer uuid;

  @TableField("watch_time")
  private Long watchTime;
  /**
   * 创建时间
   */
  @TableField("create_time")
  private Long createTime;

  /**
   * 修改时间
   */
  @TableField("update_time")
  private Long updateTime;

}
