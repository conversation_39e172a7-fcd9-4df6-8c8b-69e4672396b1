package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/1/9
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_book_relation")
public class UserBookRelationPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 联合唯一键
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 联合唯一键,uid在前
     */
    @TableField("user_id")
    private String userId;

    /**
     * 当前观看集数
     */
    @TableField("watch_chapter_id")
    private Long watchChapterId;

    /**
     * 是否收藏
     */
    @TableField("match_collect")
    private boolean matchCollect;

    /**
     * 当前解锁集数
     */
    @TableField("unlock_chapter_id")
    private Long UnlockChapterId;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;
}
