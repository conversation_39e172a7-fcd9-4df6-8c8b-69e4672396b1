package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @since 2023/12/17
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_good_info")
public class GoodsInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 唯一键
     */
    @TableField("good_id")
    private Long goodId;

    /**
     * 商品价格，美元计价，单位 ：分
     */
    @TableField(value = "price_usd_cent")
    private Long priceUsdCent;

//    /**
//     * 转换后的货币计价，分
//     */
//    @TableField("priceCent")
//    private Long priceCent;
//
//    /**
//     * 货币类型
//     */
//    @TableField("currency")
//    private String currency;

    /**
     * 奖励数量
     */
    @TableField(value = "coin_num")
    private Long coinNum;

    /**
     * 积分数量
     */
    @TableField(value = "bonus_num")
    private Long bonusNum;

    /**
     * 是否上架
     */
    @TableField(value = "match_online")
    private boolean matchOnline;

    /**
     * 最佳推荐
     */
    @TableField(value = "best")
    private boolean best;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 商品类型
     */
    @TableField("good_type")
    private Integer goodType;

    /**
     * vip时间
     */
    @TableField("vip_time")
    private Long vipTime;

    public boolean matchBanner() {
        return this.goodId == 0;
    }

}
