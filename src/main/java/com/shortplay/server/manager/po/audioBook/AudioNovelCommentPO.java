package com.shortplay.server.manager.po.audioBook;

/**
 * <AUTHOR>
 * @since 2025/7/21
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("audio_novel_comments")
public class AudioNovelCommentPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增长评论id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("novel_id")
    private Long novelId;

    @TableField("user_id")
    private String userId;

    @TableField("content")
    private String content;

    @TableField("parent_id")
    private Long parentId;

    @TableField("root_id")
    private Long rootId;

    @TableField("create_time")
    private Long createTime;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("like_count")
    private Long likeCount;

    @TableField("reply_count")
    private Long replyCount;

}
