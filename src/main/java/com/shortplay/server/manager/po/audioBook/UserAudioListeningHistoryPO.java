package com.shortplay.server.manager.po.audioBook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("user_audio_listening_history")
public class UserAudioListeningHistoryPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增长小说id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 章节id
     */
    @TableField("chapter_id")
    private Long chapterId;

    /**
     * 冗余小说ID，避免联表查询
     */
    @TableField("novel_id")
    private Long novelId;

    /**
     * 收听进度（秒），用于续播
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 设备标识（如：iPhone12,5）
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 最后收听时间
     */
    @TableField("last_play_time")
    private Long lastPlayTime;

    /**
     * 首次收听时间
     */
    @TableField("created_at")
    private Long createdAt;
}
