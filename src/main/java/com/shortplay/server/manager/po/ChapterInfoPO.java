package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/1/9
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_chapter_info")
public class ChapterInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @JsonIgnore
    private Long id;

    /**
     * 联合唯一键
     */
    @TableField("chapter_id")
    private Long chapterId;

    /**
     * 联合唯一键
     */
    @TableField("book_id")
    private Long bookId;


    /**
     * 喜爱集数
     */
    @TableField("like_num")
    private Long likeNum;

    @TableField("collect_num")
    private Long collectNum;
    /**
     * 浏览次数
     */
    @TableField("visit_num")
    private Long visitNum;

    /**
     * 存储位置
     */
    @TableField("object_key")
    private String objectKey;

    /**
     * 花费金额 standard
     */
    @TableField("cost_num")
    private Long costNum;

    @TableField("vip_cost_num")
    private Long vipCostNum;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;
}

