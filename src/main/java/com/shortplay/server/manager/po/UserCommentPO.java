package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_comment")
public class UserCommentPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("book_id")
    private Long bookId;


    @TableField("chapter_id")
    private Long chapterId;

    @TableField("uuid")
    private Integer uuid;

    @TableField("comment")
    private String content;

    /**
     * 父评论id
     */
    @TableField("parent_id")
    private Integer parentId;

    @TableField("root_id")
    private Integer rootId;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 是否删除
    * */
    @TableField("is_deleted")
    private Integer isDeleted;

}
