package com.shortplay.server.manager.po.shortVideo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_video_comment")
public class VideoCommentPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 短视频id
     */
    @TableField("video_id")
    private Long videoId;

    /**
     * 评论内容
     */
    @TableField("content")
    private String content;

    /**
     * 父评论id
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 根评论id
     */
    @TableField("root_id")
    private Long rootId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Long likeCount;

    /**
     * 回复数
     */
    @TableField("reply_count")
    private Long replyCount;

    /**
     * 用户uuid
     */
    @TableField("uuid")
    private int uuid;
}
