package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/1/9
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_chapter_factory")
public class ChapterFactoryPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 存储位置
     */
    @TableField("object_key")
    private String objectKey;

    /**
     * 剧名
     */
    @TableField("bookName")
    private String bookName;
    /**
     * 剧id
     */
    @TableField("bookId")
    private Long bookId;

    /**
     * 集数
     */
    @TableField("chapterNum")
    private String chapterNum;


    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;
}

