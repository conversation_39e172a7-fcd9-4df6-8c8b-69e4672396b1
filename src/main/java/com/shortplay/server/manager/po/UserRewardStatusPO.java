package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_reward_status")
public class UserRewardStatusPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户 ID 唯一
     */
    @TableField("userId")
    private String userId;

    /**
     * 签到天数
     */
    @TableField("signDays")
    private Integer signDays;

    /**
     * 添加到主屏幕
     */
    @TableField("addHomeScreen")
    private boolean addHomeScreen;

    @TableField("facebook")
    private int facebook;
    @TableField("instagram")
    private int instagram;
    @TableField("twitter")
    private int twitter;
    @TableField("tiktok")
    private int tiktok;
    @TableField("whatsapp")
    private int whatsapp;

    @TableField("totalBonus")
    private Long totalBonus;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;
}
