package com.shortplay.server.manager.po.shortVideo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("tbl_user_video_comment_relation")
public class UserVideoCommentRelationPO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 评论id
     */
    @TableField("comment_id")
    private Long commentId;

    /**
     * 用户uuid
     */
    @TableField("uuid")
    private int uuid;

    /**
     * 是否点赞
     */
    @TableField("match_like")
    private boolean matchLike;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Long updateTime;
}
