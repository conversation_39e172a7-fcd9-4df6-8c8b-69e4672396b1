package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("ad_unlock_history")
public class AdUnlockHistoryPO implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 系列Id
     */
    @TableField("book_id")
    private Long bookId;

    /**
     * 剧集Id
     */
    @TableField("chapter_id")
    private Long chapterId;

    /**
     * 解锁时间
     */
    @TableField("unlock_time")
    private Long unlockTime;


    /**
     * 解锁类型
     * 1=章节解锁，2=广告任务
     */
    @TableField("unlock_type")
    private Integer unlockType;
}
