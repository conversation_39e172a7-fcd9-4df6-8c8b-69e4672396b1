package com.shortplay.server.manager.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TableName("apple_store_record")
public class AppleStoreRecord {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 原始类型
     */
    @TableField("raw_type")
    private String rawType;

    /**
     * 原始撤销原因
     */
    @TableField("raw_revocation_reason")
    private Integer rawRevocationReason;

    /**
     * 原始交易原因
     */
    @TableField("raw_transaction_reason")
    private String rawTransactionReason;

    /**
     * 原始折扣类型
     */
    @TableField("raw_offer_discount_type")
    private String rawOfferDiscountType;

    /**
     * 原始拥有类型
     */
    @TableField("raw_in_app_ownership_type")
    private String rawInAppOwnershipType;

    /**
     * 原始环境
     */
    @TableField("raw_environment")
    private String rawEnvironment;

    /**
     * 原始优惠类型
     */
    @TableField("raw_offer_type")
    private Integer rawOfferType;

    /**
     * 未知字段JSON
     */
    @TableField("unknown_fields")
    private String unknownFields;

    /**
     * 原始交易ID;原始购买交易的交易标识符。
     */
    @TableField("original_transaction_id")
    private String originalTransactionId;

    /**
     * 交易ID;交易的唯一标识符
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * Web订单项目ID;跨设备订阅购买事件的唯一标识符，包括订阅续订
     */
    @TableField("web_order_line_item_id")
    private String webOrderLineItemId;

    /**
     * 应用包名
     */
    @TableField("bundle_id")
    private String bundleId;

    /**
     * 商品ID;商品的唯一标识码
     */
    @TableField("product_id")
    private String productId;

    /**
     * 订阅组标识;订阅所属的订阅组的标识符
     */
    @TableField("subscription_group_identifier")
    private String subscriptionGroupIdentifier;

    /**
     * 购买时间（时间戳）;App Store 在失效后向客户帐户收取购买、恢复产品、订阅或订阅续订费用的 UNIX 时间（以毫秒为单位）
     */
    @TableField("purchase_date")
    private Long purchaseDate;

    /**
     * 原始购买时间（时间戳）;UNIX 时间（以毫秒为单位），表示原始交易标识符的购买日期
     */
    @TableField("original_purchase_date")
    private Long originalPurchaseDate;

    /**
     * 过期时间（时间戳）;订阅过期或续订的 UNIX 时间（以毫秒为单位）
     */
    @TableField("expires_date")
    private Long expiresDate;

    /**
     * 购买数量;买家购买的消费品数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 类型，如 Consumable;应用内购买的类型
     */
    @TableField("type")
    private String type;

    /**
     * 应用账户标识;您在购买时创建的 UUID，用于将交易与您自己的服务上的客户相关联。如果您的应用程序未提供应用程序帐户令牌，则省略此字段。更多信息
     */
    @TableField("app_account_token")
    private String appAccountToken;

    /**
     * 用户拥有类型;用于描述交易是客户购买的，还是通过“家人共享”提供给他们
     */
    @TableField("in_app_ownership_type")
    private String inAppOwnershipType;

    /**
     * 签名时间（时间戳）;App Store 对 JSON Web 签名 （JWS） 数据进行签名的 UNIX 时间（以毫秒为单位）
     */
    @TableField("signed_date")
    private Long signedDate;

    /**
     * 撤销原因;App Store 退还交易或从家人共享中撤销交易的原因
     */
    @TableField("revocation_reason")
    private String revocationReason;

    /**
     * 撤销时间（时间戳）;App Store 为交易退款或从“家人共享”中撤销交易的 UNIX 时间（以毫秒为单位）
     */
    @TableField("revocation_date")
    private Long revocationDate;

    /**
     * 是否升级订阅;一个布尔值，指示客户是否升级到其他订阅
     */
    @TableField("is_upgraded")
    private Boolean isUpgraded;

    /**
     * 优惠类型;表示促销优惠类型的值。
     */
    @TableField("offer_type")
    private String offer_type;

    /**
     * 优惠标识符;包含优惠代码或促销优惠标识符的标识符
     */
    @TableField("offer_identifier")
    private String offerIdentifier;

    /**
     * 环境，如Sandbox或Production
     */
    @TableField("environment")
    private String environment;

    /**
     * 商店前端区域;三个字母的代码，表示与购买的 App Store 店面关联的国家或地区
     */
    @TableField("storefront")
    private String storefront;

    /**
     * 商店ID;一个 Apple 定义的值，用于唯一标识与购买关联的 App Store 店面
     */
    @TableField("storefront_id")
    private String storefrontId;

    /**
     * 交易原因;购买交易的原因，指示是客户的购买还是系统启动的自动续订订阅的续订
     */
    @TableField("transaction_reason")
    private String transactionReason;

    /**
     * 价格（以最小货币单位，例如分）;一个整数值，表示价格乘以您在 App Store Connect 中配置的 App 内购买或订阅优惠的 1000，并在购买时系统记录。有关更多信息，请参阅价格。currency 参数指示此价格的货币
     */
    @TableField("price")
    private Long price;

    /**
     * 货币单位，如HKD
     */
    @TableField("currency")
    private String currency;

    /**
     * 优惠折扣类型;您为订阅优惠配置的付款方式，例如 Free Trial、Pay As You Go 或 Pay Up Front
     */
    @TableField("offer_discount_type")
    private String offerDiscountType;

    /**
     * App交易ID
     */
    @TableField("app_transaction_id")
    private String appTransactionId;

    /**
     * 优惠周期;应用于交易的选件的持续时间
     */
    @TableField("offer_period")
    private String offerPeriod;

}
