package com.shortplay.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shortplay.server.manager.po.AdUnlockHistoryPO;
import com.shortplay.server.manager.po.BookInfoPO;
import com.shortplay.server.manager.po.ChapterInfoPO;
import com.shortplay.server.manager.po.UserInfoPO;

public interface AdUnlockHistoryService extends IService<AdUnlockHistoryPO> {
    void buildAndSaveUnlockHistoryPO(UserInfoPO user, BookInfoPO bookInfoPO, ChapterInfoPO chapterInfoPO);
}
