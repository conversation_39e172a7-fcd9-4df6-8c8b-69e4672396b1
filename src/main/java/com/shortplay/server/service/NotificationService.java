package com.shortplay.server.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shortplay.server.component.config.NotificationConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

/**
 * 转发 webhook 到飞书的机器人
 */
@Service
@EnableAsync
public class NotificationService {
    private final static String OPEN_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/";

    @Autowired
    NotificationConfiguration notificationConfiguration;

    /**
     * 发送带有超链接的文本消息
     *
     * @param title    消息标题
     * @param token    机器人 token
     * @param time     可选 消息上的时间
     * @param url      超链接 url
     * @param urlTitle 超链接文字
     * @return ok
     * 如：
     * 定时任务失败
     * spug 查看
     * 发生时间：2021-05-12 12:03:13
     * <p>
     * curl -d 'title=定时任务失败&token=xxx' '<a href="http://172.26.128.127:10000/feishu/bot/text">...</a>'
     */
    public ResponseEntity postMessage(@RequestParam(required = true) String title, @RequestParam(required = true) String token,
                                      @RequestParam(required = false) String time, @RequestParam(required = false) String url,
                                      @RequestParam(required = false) String urlTitle) {
        //POST请求
        List<Object> contentList = new ArrayList();
        List<Object> line1List = new ArrayList();
        List<Object> line2List = new ArrayList();


        JSONObject urlJson = JSONUtil.createObj();
        urlJson.set("tag", "a");
        urlJson.set("text", StrUtil.isBlank(urlTitle) ? "spug 查看" : urlTitle);
        urlJson.set("href", url);
        JSONObject timeJson = JSONUtil.createObj();
        timeJson.set("tag", "text");
        timeJson.set("text", "发生时间：" + (StrUtil.isBlank(time) ? DateUtil.now() : time));

        line1List.add(urlJson);
        line2List.add(timeJson);
        contentList.add(line1List);
        contentList.add(line2List);
        JSONObject zh_cn = JSONUtil.createObj();
        zh_cn.set("content", contentList);
        zh_cn.set("title", title);
        JSONObject post = JSONUtil.createObj();
        post.set("zh_cn", zh_cn);
        JSONObject content = JSONUtil.createObj();
        content.set("post", post);

        JSONObject json = JSONUtil.createObj()
                .set("msg_type", "post")
                .set("content", content);

        String result1 = HttpRequest.post(OPEN_URL + token).body(json.toString()).execute().body();
        Console.log(result1);
        return ResponseEntity.ok().build();
    }

    /**
     * @param token   验证凭据
     * @param content 消息文本内容
     * @param title   兼容之前的接口的通知格式，富文本内容的标题名，优先级高于 content 字段
     */
    public ResponseEntity textMessage(@RequestParam(required = false) String content, @RequestParam(required = true) String token,
                                      @RequestParam(required = false) String title) {
        if (StrUtil.isNotBlank(title)) {
            content = title;
        }
        JSONObject textContent = JSONUtil.createObj();
        textContent.set("text", content);

        JSONObject json = JSONUtil.createObj()
                .set("msg_type", "text")
                .set("content", textContent);

        String result1 = HttpRequest.post(OPEN_URL + token).body(json.toString()).execute().body();
        Console.log(result1);
        return ResponseEntity.ok().build();
    }

    /**
     * 服务监控
     *
     * @param msg 消息
     */
    public void sendServiceMonitorMessage(String msg, Object... params) {
        textMessage(StrUtil.format(msg, params), notificationConfiguration.getServiceMonitor(), null);
    }

}
