package com.shortplay.server.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/12/16
 **/
@Service
@Slf4j
public class JWTService {
    @Value("${jwt.key}")
    private String secretKey;

    @Value("${jwt.expire}")
    private Long tokenExpireHour;

    public String getTokenByUserId(String userId) {

        Date expireDate = new Date(System.currentTimeMillis() + TimeUnit.HOURS.toMillis(tokenExpireHour));
        //私钥加密
        Algorithm algorithm = Algorithm.HMAC256(secretKey);
        Map<String, Object> header = new HashMap<>();
        header.put("typ", "JWT");
        header.put("alg", "Hs256");
        return JWT.create().withHeader(header)
                .withClaim("userId", userId)
                .withExpiresAt(expireDate).sign(algorithm);

    }

    public String getUserIdByToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw BizException.of(BizCode.LOGIN_ERROR, "token is Empty");
        }
        return getClaim(token, "userId");
    }

    private String getClaim(String token, String claim) {
        verify(token);
        try {
            return JWT.decode(token).getClaim(claim).asString();
        } catch (JWTDecodeException e) {
            log.error("getClaim, token 不存在, error is {}", ExceptionUtil.stacktraceToString(e));
        }
        throw BizException.of(BizCode.LOGIN_ERROR, "getClaim, token 不存在" + claim);
    }


    private void verify(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secretKey);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
        } catch (Exception e) {
            log.error("verify, token 不合法, error is {}", ExceptionUtil.stacktraceToString(e));
            throw BizException.of(BizCode.LOGIN_ERROR, "token 不合法");
        }
    }
}
