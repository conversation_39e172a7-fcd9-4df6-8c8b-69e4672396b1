package com.shortplay.server.service.impl.oauth;

import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.ws.rs.POST;
import java.io.UnsupportedEncodingException;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Service
public class TiktokOauthService {
    private final RestTemplate restTemplate = new RestTemplate();

    private static final String CLIENT_KEY = "sbawwjfsvg5z0lacsi";
    private static final String CLIENT_SECRET = "nKDMr7RkCy44rbVsscGMb6uyNN7vGaCI";
    public Map<String, Object> getTiktokId(String code,String redirectUri) {
        String url = "https://open.tiktokapis.com/v2/oauth/token/";
                   // String url ="https://open-api.tiktok.com/oauth/access_token/";
        // 手动构建 x-www-form-urlencoded 格式字符串
//        String form = String.format(
//                "client_key=%s&client_secret=%s&code=%s&grant_type=authorization_code&redirect_uri=%s",
//                URLEncoder.encode(CLIENT_KEY, StandardCharsets.UTF_8),
//                URLEncoder.encode(CLIENT_SECRET, StandardCharsets.UTF_8),
//                URLEncoder.encode(code, StandardCharsets.UTF_8),
//               // URLEncoder.encode("https://www.startv.ae/", StandardCharsets.UTF_8)
//                URLEncoder.encode("https://redirectmeto.com/startv://auth", StandardCharsets.UTF_8)
//        );
        String form = String.format(
                "client_key=%s&client_secret=%s&code=%s&grant_type=authorization_code&redirect_uri=%s",
                URLEncoder.encode(CLIENT_KEY, StandardCharsets.UTF_8),
                URLEncoder.encode(CLIENT_SECRET, StandardCharsets.UTF_8),
                URLEncoder.encode(code, StandardCharsets.UTF_8),
                // URLEncoder.encode("https://www.startv.ae/", StandardCharsets.UTF_8)
                URLEncoder.encode(redirectUri, StandardCharsets.UTF_8)
                //,URLEncoder.encode("1uIqqQzFKXR__B3QqK4tiEfyapSsDcz2UMWdx4LsP1OY", StandardCharsets.UTF_8)
        );

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.setCacheControl(CacheControl.noCache());

        HttpEntity<String> request = new HttpEntity<>(form, headers);

        try {
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, request, Map.class);
            Map<String, Object> responseBody = response.getBody();
            System.out.println("TikTok response: " + responseBody);

            if (responseBody != null && responseBody.containsKey("access_token")) {
                return responseBody;
            } else {
                throw new RuntimeException("Failed to obtain TikTok ID or token: " + responseBody);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Exception when getting TikTok token", e);
        }
    }


    public Map getUserInfo(String accessToken, String openId) {

        // 设置代理
//        System.setProperty("https.proxyHost", "127.0.0.1");
//        System.setProperty("https.proxyPort", "7890");

        // TikTok API 地址 + 请求字段
        String fields = "open_id,avatar_url,display_name";
        String url = "https://open.tiktokapis.com/v2/user/info/?fields=" + fields;

        // 设置请求头 Authorization
        HttpHeaders headers = new HttpHeaders();
         headers.setBearerAuth(accessToken);  // 相当于 "Authorization: Bearer your_token"
        //headers.setBearerAuth("act.p078siYgfwxu8sxdkuNB93bemMgV3JO4RmjKxXT3jhBfieLHsaqjMfQFWBPU!5304.va");
       // headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        // 发送 GET 请求
      try {
          ResponseEntity<Map> response = restTemplate.exchange(
                  url,
                  HttpMethod.GET,
                  entity,
                  Map.class
          );
          Map data = (Map) response.getBody().get("data");
          Map userInfo = (Map) data.get("user");
          if (userInfo == null) {
              throw new RuntimeException("Failed to obtain user information");
          }
          return userInfo;
      } catch (Exception e) {
          e.printStackTrace();
          throw new RuntimeException("Exception when getting user information", e);
      }
    }
}
