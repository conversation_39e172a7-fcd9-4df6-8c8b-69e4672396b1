package com.shortplay.server.service.impl;


import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.RandomUtil;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.component.enums.EmailTypeEnum;
import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.activation.FileDataSource;
import jakarta.mail.*;
import jakarta.mail.internet.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @since 2023/12/16
 **/
@Service
@Slf4j
public class MailService {
    @Resource
    RedisTemplate<String, String> redisTemplate;

    @Value("${mail.user}")
    private String user;

    @Value("${mail.from}")
    private String from;

    @Value("${mail.password}")
    private String password;

    //失效时间和重新发送时间,单位分钟
    @Value("${mail.expireMinute}")
    private long expireMinute;

    @Value("${mail.intervalMinute}")
    private long intervalMinute;

    private static final String MAIL_SMTP_HOST = "smtp.zoho.com";
    private static final String MAIL_SMTP_PORT = "587";

    public void sendVerifyCode(String receive, EmailTypeEnum emailTypeEnum) {
        sendVerifyCode(receive, RandomUtil.randomNumbers(6), emailTypeEnum);
    }

    private void sendVerifyCode(String receive, String code, EmailTypeEnum emailTypeEnum) {
        receive = receive.toLowerCase(Locale.ROOT);
        String key = emailTypeEnum.getKey(receive);
        verifySendTime(key);
        sendEmail("[INSSHORT] Verification Code", "Hello, Your INSSHORT verification code: <br><br><strong>" + code + "<strong/><br><br>" +
                        "The verification code will be valid for " + expireMinute + " minutes. Please do not share this code with anyone.",
                List.of(), new String[]{receive}, new String[]{}, new String[]{});
        redisTemplate.opsForValue().set(key, code, expireMinute, TimeUnit.MINUTES);
    }

    private void verifySendTime(String key) {
        Long expire = redisTemplate.getExpire(key);
        if (expire == null) {
            return;
        }
        if (TimeUnit.MINUTES.toSeconds(expireMinute) - expire >= TimeUnit.MINUTES.toSeconds(intervalMinute)) {
            return;
        }
        throw BizException.of(BizCode.SEND_MAIL_ERROR, "email has been send");
    }

    private void sendEmail(String theme, String content, List<File> files, String[] sendTo, String[] sendCopyTo, String[] sendSecretTo) {
        try (Transport transport = getSession().getTransport()) {
            //3、创建邮件的实例对象
            Message msg = getMessage(getSession(), theme, content, files, sendTo, sendCopyTo, sendSecretTo);
            //设置发件人的账户名和密码
            transport.connect(user, password);
            //发送邮件，并发送到所有收件人地址，message.getAllRecipients() 获取到的是在创建邮件对象时添加的所有收件人, 抄送人, 密送人
            transport.sendMessage(msg, msg.getAllRecipients());
        } catch (Exception e) {
            log.error("发送邮件失败, address:{}, error si {}", Arrays.toString(sendTo), ExceptionUtil.stacktraceToString(e));
            throw BizException.of(BizCode.SEND_MAIL_ERROR);
        }
    }


    private static Session getSession() throws Exception {
        Properties props = new Properties();
        //设置用户的认证方式
        props.setProperty("mail.smtp.auth", "true");
        //设置传输协议
        props.setProperty("mail.transport.protocol", "smtp");
        //设置发件人的SMTP服务器地址
        props.put("mail.smtp.starttls.enable", "true");
        // 指定安全协议
        props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        props.setProperty("mail.smtp.host", MAIL_SMTP_HOST);
        props.setProperty("mail.smtp.port", MAIL_SMTP_PORT);

        return Session.getInstance(props);
    }

    private MimeMessage getMessage(Session session, String theme, String content, List<File> files, String[] sendTo, String[] sendCopyTo, String[] sendSecretTo) throws Exception {

        //创建一封邮件的实例对象
        MimeMessage mimeMessage = new MimeMessage(session);
        //设置发件人地址
        mimeMessage.setFrom(new InternetAddress(from));
        /*
         * 设置收件人地址（可以增加多个收件人、抄送、密送），即下面这一行代码书写多行
         * MimeMessage.RecipientType.TO:发送
         * MimeMessage.RecipientType.CC：抄送
         * MimeMessage.RecipientType.BCC：密送
         */

        if (null != sendTo && sendTo.length > 0) {
            mimeMessage.addRecipients(MimeMessage.RecipientType.TO, buildAddress(sendTo));
        }
        if (null != sendCopyTo && sendCopyTo.length > 0) {
            mimeMessage.addRecipients(MimeMessage.RecipientType.CC, buildAddress(sendCopyTo));
        }
        if (null != sendSecretTo && sendSecretTo.length > 0) {
            mimeMessage.addRecipients(MimeMessage.RecipientType.BCC, buildAddress(sendSecretTo));
        }
        //设置邮件主题
        mimeMessage.setSubject(theme, "UTF-8");
        //设置邮件正文


        Multipart multipart = new MimeMultipart();

        // 添加邮件正文
        BodyPart contentPart = new MimeBodyPart();
        contentPart.setContent(content, "text/html;charset=UTF-8");
        multipart.addBodyPart(contentPart);
        BodyPart attachmentBodyPart;
        DataSource source;
        for (File file : files) {
            attachmentBodyPart = new MimeBodyPart();
            source = new FileDataSource(file);
            attachmentBodyPart.setDataHandler(new DataHandler(source));
            attachmentBodyPart.setFileName(MimeUtility.encodeWord(file.getName()));
            multipart.addBodyPart(attachmentBodyPart);
        }

        mimeMessage.setContent(multipart);

        return mimeMessage;
    }

    private Address[] buildAddress(String[] address) throws AddressException {
        InternetAddress[] result = new InternetAddress[address.length];
        for (int i = 0; i < address.length; i++) {
            result[i] = new InternetAddress(address[i]);
        }
        return result;
    }
}
