package com.shortplay.server.service.impl.oauth;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.facebook.ads.sdk.APIContext;
import com.facebook.ads.sdk.APINodeList;
import com.facebook.ads.sdk.AdAccount;
import com.facebook.ads.sdk.Campaign;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.github.scribejava.core.pkce.PKCE;
import com.github.scribejava.core.pkce.PKCECodeChallengeMethod;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.auth.TwitterOAuth20Service;
import com.twitter.clientlib.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/12/24
 **/
@Service
@Slf4j
public class FacebookOauthService {

    @Resource
    private FacebookOauthService facebookOauthService;

    @Value("${authorization.twitter.clientId}")
    private String twitterClientId;

    @Value("${authorization.twitter.clientSecret}")
    private String twitterClientSecret;
    private static final String CODE_CHALLENGE = "challenge";
    public static final String ACCESS_TOKEN = "***************";
    public static final String APP_SECRET = "fa5911faaecad7dc6bceb2052549fa39";
    public static final Long ACCOUNT_ID = 1L;

    public String getAuthorizingUrl() {
        new APIContext(
                "****************",
                "d8a67fb1e1169ba29e17bad2272728e9");
        String secretState = "state";

        return "https://www.facebook.com/v19.0/dialog/oauth?" +
                "  client_id={****************}" +
                "  &redirect_uri={\"https://www.domain.com/login\"}" +
                "  &state={\"{st=state123abc,ds=*********}\"}";
    }

    public String authorizeAndGetUserId(String code) {
        String res = null;
        try {
            HttpResponse response = HttpRequest.get(StrUtil.format("https://graph.facebook.com/debug_token?access_token={}%7C{}&input_token={}",
                            ACCESS_TOKEN, APP_SECRET, code))
                    .execute();
            res = response.body();
            JSONObject json = JSONUtil.parseObj(response.body());
            boolean isValid = json.getJSONObject("data").getBool("is_valid");
            String userId = json.getJSONObject("data").getStr("user_id");
            if (isValid) {
                return userId;
            }
        } catch (Exception e) {
            log.error("FacebookOauthService authorizeAndGetUserId error, res: {}, code: {}", res, code, e);
        }
        throw new BizException(BizCode.AUTH_FAIL, "Facebook");
    }

    private PKCE getPkce() {
        PKCE pkce = new PKCE();
        pkce.setCodeChallenge(CODE_CHALLENGE);
        pkce.setCodeChallengeMethod(PKCECodeChallengeMethod.PLAIN);
        pkce.setCodeVerifier(CODE_CHALLENGE);
        return pkce;
    }
}
