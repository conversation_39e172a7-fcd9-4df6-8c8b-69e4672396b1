package com.shortplay.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.manager.UserChapterRelationMapper;
import com.shortplay.server.manager.UserWatchTimeMapper;
import com.shortplay.server.manager.po.UserChapterRelationPO;
import com.shortplay.server.manager.po.UserWatchTimePO;
import com.shortplay.server.service.UserChapterRelationService;
import com.shortplay.server.service.UserWatchTimeService;
import org.springframework.stereotype.Service;

@Service
public class UserWatchTimeServiceImpl extends ServiceImpl<UserWatchTimeMapper, UserWatchTimePO> implements UserWatchTimeService {
}
