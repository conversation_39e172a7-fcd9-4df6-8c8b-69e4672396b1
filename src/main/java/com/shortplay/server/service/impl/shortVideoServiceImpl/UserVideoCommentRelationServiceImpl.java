package com.shortplay.server.service.impl.shortVideoServiceImpl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.shortVideo.UserVideoCommentRelationPO;
import com.shortplay.server.manager.po.shortVideo.VideoCommentPO;
import com.shortplay.server.manager.shortVideoMapper.UserVideoCommentRelationMapper;
import com.shortplay.server.service.shortVideoService.UserVideoCommentRelationService;
import com.shortplay.server.service.shortVideoService.VideoCommentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class UserVideoCommentRelationServiceImpl extends ServiceImpl<UserVideoCommentRelationMapper, UserVideoCommentRelationPO> implements UserVideoCommentRelationService {

    @Resource
    private VideoCommentService videoCommentService;

    @Override
    public void videoCommentLike(UserInfoPO userInfo, VideoCommentPO videoCommentInfo) {
        if(userInfo != null){
            UserVideoCommentRelationPO userVideoCommentRelationPO = this.getAndSaveUserVideoCommentrelationPO(userInfo, videoCommentInfo);
            if(!userVideoCommentRelationPO.isMatchLike()){
                userVideoCommentRelationPO.setMatchLike(true);
                saveOrUpdate(userVideoCommentRelationPO);
            }
        }
        videoCommentInfo.setLikeCount(videoCommentInfo.getLikeCount() + 1);
        videoCommentService.saveOrUpdate(videoCommentInfo);
    }

    private UserVideoCommentRelationPO getAndSaveUserVideoCommentrelationPO(UserInfoPO userInfo, VideoCommentPO videoCommentInfo) {
        Optional<UserVideoCommentRelationPO> userVideoCommentRelationOpt = this.lambdaQuery()
                .eq(UserVideoCommentRelationPO::getCommentId, videoCommentInfo.getId())
                .eq(UserVideoCommentRelationPO::getUuid, userInfo.getUuid()).oneOpt();
        if(userVideoCommentRelationOpt.isPresent()){
            return userVideoCommentRelationOpt.get();
        }
        UserVideoCommentRelationPO userVideoCommentRelationPO = buildUserVideoCommentRelationPO(userInfo,videoCommentInfo);
        boolean save = this.save(userVideoCommentRelationPO);
        if (!save) {
            throw BizException.of(BizCode.DATA_SAVE_FAILED, "UserVideoCommentRelationPO");
        }
        return userVideoCommentRelationPO;
    }

    private UserVideoCommentRelationPO buildUserVideoCommentRelationPO(UserInfoPO userInfo, VideoCommentPO videoCommentInfo) {
        UserVideoCommentRelationPO userVideoCommentRelationPO = new UserVideoCommentRelationPO();
        userVideoCommentRelationPO.setCommentId(videoCommentInfo.getId());
        userVideoCommentRelationPO.setUuid(userInfo.getUuid());
        userVideoCommentRelationPO.setMatchLike(false);
        userVideoCommentRelationPO.setCreateTime(DateUtil.current());
        userVideoCommentRelationPO.setUpdateTime(DateUtil.current());
        return userVideoCommentRelationPO;
    }
}
