package com.shortplay.server.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.manager.ConfigMapper;
import com.shortplay.server.manager.po.ConfigPO;
import com.shortplay.server.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, ConfigPO> implements ConfigService {

    public static final String BANNER_CONFIG = "bannerConfig";
    public static final String NEW_USER_BONUS = "newUserBonus";
    public static final String WEBSITE_TITLE = "websiteTitle";
    public static final String WEBSITE_ICON = "websiteIcon";
    public static final String WEBSITE_LOGO = "websiteLogo";
    public static final String WEBSITE_HOST = "websiteHost";
    public static final String COPY_RIGHT = "copyRight";
    public static final String TWITTER_LINK = "twitterLink";
    public static final String TELEGRAM_LINK = "telegramLink";
    public static final String MEDIUM_LINK = "mediumLink";
    public static final String DISCORD_LINK = "discordLink";
    public static final String SHARE_FACEBOOK = "shareFacebook";
    public static final String SHARE_TWITTER = "shareTwitter";
    public static final String SHARE_TIK_TOK = "shareTikTok";
    public static final String SHARE_WHATS_APP= "shareWhatsApp";
    @Override
    public void initConfig() {
        ConfigPO bannerConfig = lambdaQuery()
                .eq(ConfigPO::getName, BANNER_CONFIG)
                .one();
        if (bannerConfig == null) {
            bannerConfig = new ConfigPO();
            bannerConfig.setName(BANNER_CONFIG);
            bannerConfig.setValue(List.of().toString());
            saveOrUpdate(bannerConfig);
            log.info("bannerConfig init.");
        }

        ConfigPO bonusConfig = lambdaQuery()
                .eq(ConfigPO::getName, NEW_USER_BONUS)
                .one();
        if (bonusConfig == null) {
            bannerConfig = new ConfigPO();
            bannerConfig.setName(NEW_USER_BONUS);
            bannerConfig.setValue("0");
            saveOrUpdate(bannerConfig);
            log.info("bonusConfig init.");
        }
        for (String name : getWebsiteConfigList()) {
            ConfigPO config = lambdaQuery()
                    .eq(ConfigPO::getName, name)
                    .one();
            if (config == null) {
                config = new ConfigPO();
                config.setName(name);
                config.setValue("");
                saveOrUpdate(config);
                log.info("{} init.", name);
            }
        }
    }

    public List<Long> getBannerBookList() {
        ConfigPO configCenter = lambdaQuery()
                .eq(ConfigPO::getName, BANNER_CONFIG)
                .one();
        if (Objects.isNull(configCenter)) {
            return Collections.emptyList();
        }
        return List.of(Convert.toLongArray(configCenter.getValue()));
    }

    public List<Long> setBannerBookList(List<Long> ids) {

        ConfigPO configCenter = lambdaQuery()
                .eq(ConfigPO::getName, BANNER_CONFIG)
                .one();
        if (configCenter == null) {
            configCenter = new ConfigPO();
            configCenter.setName(BANNER_CONFIG);
        }
        configCenter.setValue(ids.stream()
                .map(Object::toString)
                .collect(Collectors.joining(",")));
        saveOrUpdate(configCenter);
        return ids;
    }

    public Long getNewUserBonus() {
        ConfigPO configCenter = lambdaQuery()
                .eq(ConfigPO::getName, NEW_USER_BONUS)
                .one();
        if (configCenter == null) {
            return 0L;
        }
        try {
            return Long.parseLong(configCenter.getValue());
        } catch (Exception e) {
            log.error("getNewUserBonus error", e);
            return 0L;
        }
    }

    private List<String> getWebsiteConfigList() {
        return List.of(
                WEBSITE_TITLE, WEBSITE_LOGO, WEBSITE_ICON, WEBSITE_HOST, COPY_RIGHT,
                TWITTER_LINK, TELEGRAM_LINK, MEDIUM_LINK, DISCORD_LINK, NEW_USER_BONUS,
                SHARE_FACEBOOK, SHARE_TWITTER, SHARE_TIK_TOK, SHARE_WHATS_APP
        );
    }

    public List<ConfigPO> getWebsiteConfig() {
        return lambdaQuery().in(ConfigPO::getName, getWebsiteConfigList()).list();
    }
}

