package com.shortplay.server.service.impl.audioBookServiceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.shortplay.server.manager.audioBookMapper.UserNovelCommentRelationMapper;
import com.shortplay.server.manager.po.UserInfoPO;
import com.shortplay.server.manager.po.audioBook.AudioNovelCommentPO;
import com.shortplay.server.manager.po.audioBook.UserNovelCommentRelationPO;
import com.shortplay.server.service.audioBookService.AudioNovelCommentService;
import com.shortplay.server.service.audioBookService.UserNovelCommentRelationService;
import com.shortplay.server.service.audioBookService.UserNovelRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class UserNovelCommentRelationServiceImpl extends ServiceImpl<UserNovelCommentRelationMapper, UserNovelCommentRelationPO> implements UserNovelCommentRelationService {

    @Resource
    private AudioNovelCommentService audioNovelCommentService;

    @Override
    public void like(UserInfoPO userInfo, AudioNovelCommentPO audioNovelCommentInfo, boolean like) {
        UserNovelCommentRelationPO userNovelCommentRelationPO = this.getAndSaveUserNovelCommentRelationPO(userInfo, audioNovelCommentInfo);
        userNovelCommentRelationPO.setMatchLike(like);
        Long current = audioNovelCommentInfo.getLikeCount();
        Long count = (current != null)? current:0L;
        audioNovelCommentInfo.setLikeCount(like?count+1:count-1);
        audioNovelCommentService.updateById(audioNovelCommentInfo);
        saveOrUpdate(userNovelCommentRelationPO);
    }

    private UserNovelCommentRelationPO getAndSaveUserNovelCommentRelationPO(UserInfoPO userInfo, AudioNovelCommentPO audioNovelCommentInfo) {
        Optional<UserNovelCommentRelationPO> userNovelCommentRelationOpt = this.lambdaQuery().eq(UserNovelCommentRelationPO::getCommentId, audioNovelCommentInfo.getId())
                .eq(UserNovelCommentRelationPO::getUserId, userInfo.getUserId()).oneOpt();
        if(userNovelCommentRelationOpt.isPresent()){
            return userNovelCommentRelationOpt.get();
        }
        UserNovelCommentRelationPO userNovelCommentRelationPO = buildUserNovelCommentRelationPO(userInfo, audioNovelCommentInfo);
        boolean save = this.save(userNovelCommentRelationPO);
        if (!save) {
            throw BizException.of(BizCode.DATA_SAVE_FAILED, "UserNovelCommentRelationPO");
        }
        return userNovelCommentRelationPO;
    }

    private UserNovelCommentRelationPO buildUserNovelCommentRelationPO(UserInfoPO userInfo, AudioNovelCommentPO audioNovelCommentInfo) {
        UserNovelCommentRelationPO userNovelCommentRelationPO = UserNovelCommentRelationPO.builder()
                .commentId(audioNovelCommentInfo.getId())
                .userId(userInfo.getUserId())
                .novelId(audioNovelCommentInfo.getNovelId())
                .matchLike(false)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
        return userNovelCommentRelationPO;
    }


}
