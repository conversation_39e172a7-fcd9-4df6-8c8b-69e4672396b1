package com.shortplay.server.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.shortplay.server.component.dto.IPGeoObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2023/12/16
 **/
@Service
@Slf4j
public class IPService {

    @Resource
    private HttpServletRequest request;

    @Value("${tz.endpoint}")
    private String endpoint;

    @Value("${tz.key}")
    private String key;

    public String getRequestIP() {
        // 获取IP地址
        String ipAddress = request.getRemoteAddr();

        // 如果通过代理进来，则透过防火墙获取真实IP地址
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (xForwardedForHeader != null) {
            ipAddress = xForwardedForHeader.split(",")[0];
        }

        return ipAddress;
    }

    public IPGeoObject getRequestTZ() {
        String ip = getRequestIP();
        return getRequestTZ(ip);
    }

    public IPGeoObject getRequestTZ(String ip) {
        IPGeoObject geoObject = IPGeoObject.builder()
                .ip(ip)
                .build();
        try {
            String res = HttpRequest.get(StrUtil.format("{}?apiKey={}&ip={}", endpoint, key, ip)).execute().body();
            log.info("getRequestTZ, ip: {}, res: {}", ip, res);
            JSONObject object = JSONUtil.parseObj(res);
            String countryName = object.getStr("country_name");
            geoObject.setCountryName(countryName);
            String stateProvince = object.getStr("state_prov");
            geoObject.setStateProvince(stateProvince);
            int offset = object.getJSONObject("time_zone").getInt("offset");
            geoObject.setOffset(offset);
            log.info("getRequestTZ, geoObject: {}", geoObject);
        } catch (Exception e) {
            log.error("getRequestTZ error, geoObject: {}", geoObject, e);
        }
        return geoObject;
    }
}