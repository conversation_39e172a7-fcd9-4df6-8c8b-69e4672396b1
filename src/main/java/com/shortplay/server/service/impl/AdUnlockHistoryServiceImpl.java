package com.shortplay.server.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.manager.AdUnlockHistoryMapper;
import com.shortplay.server.manager.po.*;
import com.shortplay.server.service.AdUnlockHistoryService;
import org.springframework.stereotype.Service;

@Service
public class AdUnlockHistoryServiceImpl extends ServiceImpl<AdUnlockHistoryMapper, AdUnlockHistoryPO> implements AdUnlockHistoryService {
    @Override
    public void buildAndSaveUnlockHistoryPO(UserInfoPO userInfo, BookInfoPO bookInfoPO, ChapterInfoPO chapterInfoPO) {
        AdUnlockHistoryPO adUnlockHistoryPO = new AdUnlockHistoryPO();
        adUnlockHistoryPO.setUserId(userInfo == null ? null :userInfo.getUserId());
        adUnlockHistoryPO.setBookId(bookInfoPO.getBookId());
        adUnlockHistoryPO.setChapterId(chapterInfoPO.getChapterId());
        adUnlockHistoryPO.setUnlockTime(DateUtil.current());

        this.save(adUnlockHistoryPO);
    }
}
