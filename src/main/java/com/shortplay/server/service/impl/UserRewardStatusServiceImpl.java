package com.shortplay.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.manager.UserRewardStatusMapper;
import com.shortplay.server.manager.UserSignRecordMapper;
import com.shortplay.server.manager.po.UserRewardStatusPO;
import com.shortplay.server.manager.po.UserSignRecordPO;
import com.shortplay.server.service.UserRewardStatusService;
import com.shortplay.server.service.UserSignRecordService;
import org.springframework.stereotype.Service;

@Service
public class UserRewardStatusServiceImpl extends ServiceImpl<UserRewardStatusMapper, UserRewardStatusPO> implements UserRewardStatusService {
}
