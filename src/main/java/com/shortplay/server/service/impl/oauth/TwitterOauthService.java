package com.shortplay.server.service.impl.oauth;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.github.scribejava.core.pkce.PKCE;
import com.github.scribejava.core.pkce.PKCECodeChallengeMethod;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import com.twitter.clientlib.TwitterCredentialsOAuth2;
import com.twitter.clientlib.api.TwitterApi;
import com.twitter.clientlib.auth.TwitterOAuth20Service;
import com.twitter.clientlib.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/12/24
 **/
@Service
@Slf4j
public class TwitterOauthService {

    @Resource
    private TwitterOAuth20Service twitterOAuth20Service;

    @Value("${authorization.twitter.clientId}")
    private String twitterClientId;

    @Value("${authorization.twitter.clientSecret}")
    private String twitterClientSecret;
    private static final String CODE_CHALLENGE = "challenge";

    public String getAuthorizingUrl() {
        String secretState = "state";
        return twitterOAuth20Service.getAuthorizationUrl(this.getPkce(), secretState);
    }

    public String authorizeAndGetUserId(String code) {
        try {
            OAuth2AccessToken accessToken = twitterOAuth20Service.getAccessToken(getPkce(), code);
            TwitterCredentialsOAuth2 oAuth2 = new TwitterCredentialsOAuth2(
                    twitterClientId,
                    twitterClientSecret,
                    accessToken.getAccessToken(),
                    accessToken.getRefreshToken(),
                    false
            );
            TwitterApi apiInstance = new TwitterApi(oAuth2);
            User user = apiInstance.users().findMyUser().execute().getData();
            if (user != null) {
                return user.getId();
            }
        } catch (Exception e) {
            log.error("auth error, error is {}", ExceptionUtil.stacktraceToString(e));
        }
        throw new BizException(BizCode.AUTH_FAIL, "twitter");
    }

    private PKCE getPkce() {
        PKCE pkce = new PKCE();
        pkce.setCodeChallenge(CODE_CHALLENGE);
        pkce.setCodeChallengeMethod(PKCECodeChallengeMethod.PLAIN);
        pkce.setCodeVerifier(CODE_CHALLENGE);
        return pkce;
    }
}
