package com.shortplay.server.service.impl.oauth;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.google.api.client.googleapis.auth.oauth2.*;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.JsonFactory;
//import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.shortplay.server.component.common.BizCode;
import com.shortplay.server.component.common.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/16
 **/
@Service
@Slf4j
public class GoogleOauthService {

    private static final List<String> scopes = List.of(
            "email",
            "profile",
            "openid"
    );

    @Resource
    private GoogleClientSecrets googleClientSecrets;

    // 导包信息
    public String authorizeAndGetEmail(String authorizationCode, String redirectUrl) {
        // 1.创建请求凭证
        try {
            HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            JsonFactory jsonFactory = JacksonFactory.getDefaultInstance();
            GoogleAuthorizationCodeFlow googleAuthorizationCodeFlow = new GoogleAuthorizationCodeFlow
                    .Builder(httpTransport, jsonFactory, googleClientSecrets, scopes).build();
            GoogleAuthorizationCodeTokenRequest tokenRequest = googleAuthorizationCodeFlow.newTokenRequest(authorizationCode);
            tokenRequest.setRedirectUri(redirectUrl);
            // 2.发起授权请求，获得授权信息
            GoogleTokenResponse tokenResponse = tokenRequest.execute();

            //3.解析返回并得到email
            String email = null;
            if (StringUtils.isNotBlank(tokenResponse.getIdToken())) {
                GoogleIdTokenVerifier idTokenVerifier = new GoogleIdTokenVerifier.Builder(googleAuthorizationCodeFlow.getTransport(), googleAuthorizationCodeFlow.getJsonFactory()).build();
                idTokenVerifier.verify(tokenResponse.getIdToken());
                GoogleIdToken googleIdToken = idTokenVerifier.verify(tokenResponse.getIdToken());
                if (googleIdToken != null && googleIdToken.getPayload() != null) {
                    email = googleIdToken.getPayload().getEmail();
                }
            }
            if (StringUtils.isNotBlank(email)) {
                return email;
            }
        } catch (GeneralSecurityException | IOException e) {
            log.error("authorizeAndGetEmail error, error is {}", ExceptionUtil.stacktraceToString(e));
        }
        throw BizException.of(BizCode.AUTH_FAIL, "google");
    }



    public String getAuthorizingUrl(String redirectUrl) {
        HttpTransport httpTransport = null;
        try {
            httpTransport = GoogleNetHttpTransport.newTrustedTransport();
            JsonFactory jsonFactory = JacksonFactory.getDefaultInstance();
            // 创建验证流程对象
            GoogleAuthorizationCodeFlow googleAuthorizationCodeFlow = new GoogleAuthorizationCodeFlow
                    .Builder(httpTransport, jsonFactory, googleClientSecrets, scopes).build();
            if (googleAuthorizationCodeFlow != null) {
               // String redirectUri = "https://www.startv.ae"; //回调地址
                // 返回跳转登录请求
                return googleAuthorizationCodeFlow.newAuthorizationUrl()
                        .setRedirectUri(redirectUrl)
                        .build();
            }
        } catch (GeneralSecurityException | IOException e) {
            log.error("getAuthorizingUrl error, error is {}", ExceptionUtil.stacktraceToString(e));
        }
        throw new BizException(BizCode.GET_AUTH_URL_ERROR, "google");
    }
}
