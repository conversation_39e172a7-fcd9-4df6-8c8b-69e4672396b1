package com.shortplay.server.service.impl.rebateServiceImpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shortplay.server.manager.po.rebate.RebatePO;
import com.shortplay.server.manager.rebateMapper.RebateMapper;
import com.shortplay.server.service.rebateService.RebateService;
import org.springframework.stereotype.Service;

@Service
public class RebateServiceImpl  extends ServiceImpl<RebateMapper, RebatePO> implements RebateService {

    @Override
    public RebatePO getLastOne() {
        return lambdaQuery().orderByDesc(RebatePO::getGmtCreate).last("limit 1").one();
    }
}




