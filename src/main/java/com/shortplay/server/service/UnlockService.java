package com.shortplay.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shortplay.server.manager.po.BookInfoPO;
import com.shortplay.server.manager.po.ChapterInfoPO;
import com.shortplay.server.manager.po.UnlockHistoryPO;
import com.shortplay.server.manager.po.UserInfoPO;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/1/14
 **/
public interface UnlockService extends IService<UnlockHistoryPO> {
    @Transactional
    void unlock(BookInfoPO bookInfoPO, ChapterInfoPO chapterInfoPO, UserInfoPO userInfoPO);
    void buildAndSaveUnlockHistoryPO(UserInfoPO userInfoPO, BookInfoPO bookInfoPO, ChapterInfoPO chapterInfoPO, boolean free,long cost);
}
