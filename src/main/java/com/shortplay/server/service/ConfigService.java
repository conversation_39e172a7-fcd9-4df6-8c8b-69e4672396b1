package com.shortplay.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shortplay.server.manager.po.ConfigPO;

import java.util.HashMap;
import java.util.List;

public interface ConfigService extends IService<ConfigPO> {
    void initConfig();

    Long getNewUserBonus();

    List<Long> getBannerBookList();

    List<Long> setBannerBookList(List<Long> ids);

    List<ConfigPO> getWebsiteConfig();
}
