spring:
  redis:
    database: 1
    host: 127.0.0.1
    port: 6389

  datasource:
    url: ************************************************************************************************
    username: now_short_user
    password: m2eJJlYvQLSD7v7fLJII
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # druid 数据源专有配置
    druid:
      # 初始化大小，最小，最大
      initial-size: 5
      minIdle: 5
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validation-query: SELECT 1 FROM DUAL

jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      limit: 100
server:
  port: 18080
  servlet:
    context-path: /api

# Knife4j配置
knife4j:
  enable: false

authorization:
  google:
    clientId: ************-qhpclknesi2drjrk4nesg1vhm6anm6dd.apps.googleusercontent.com
    clientSecret: 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
  twitter:
    clientId: NkFIdHRGNjVrd1FwWHI4TjlBaEY6MTpjaQ
    clientSecret: dLw5I-LMhJU1XlKyXngiaon_huxa06kLemmbYSzcWeRDtY26uD
    callback: https://dev.nowshort.com/

paypal:
  mode: sandbox
  clientId: AR1S9z4PinEj2auup2qfeILX9Hn0rJVOxC4iNBnDcXYi5e2Gz3GTVkP80fcKzCWnhkggRtEGOpkfKvzV
  clientSecret: EL8Owd6WIv1qe6z2GMjIr_DUK5LLeLfHlUa-7nhzWMBsGBAepRiJIeXZazlQaKSm6Tj_XzLOD8IHeGzp
  webhookId: 2WY65981GF830370K

amazon:
  resourceUrl: https://test-stream.nowshort.com/assets/
  avatarUrl: https://test-stream.nowshort.com/avatar/
  privateKeyPath: /home/<USER>/.ssh/cf_private_key.pem
  keyPairId: K324C9KZLBZ0R6
  outputBucket: vod-insshort-output
#  ak: ********************
#  sk: p/4DAs2S5wk4GBPYxbmICew4AKN8gXvljh/Yym+J

jwt:
  key: M4S25iPYNloLSW04tDeHsZnWhOCB727MaLKvU0PB
  expire: 720

mail:
  from: <EMAIL>
  user: <EMAIL>
  password: X.*Yff*YM~C5C@^
  expireMinute: 10
  intervalMinute: 1
tz:
  endpoint: https://api.ipgeolocation.io/ipgeo
  key: ********************************